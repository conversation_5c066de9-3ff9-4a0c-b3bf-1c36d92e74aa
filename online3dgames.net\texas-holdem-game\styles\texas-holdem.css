
/* Icon Styles */
.icon {
    display: inline-block;
    font-style: normal;
    font-size: 1em;
    line-height: 1;
    vertical-align: middle;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #0f4c3a 0%, #1a5f4a 50%, #0f4c3a 100%);
    min-height: 100vh;
    color: #fff;
    overflow-x: hidden;
}

.game-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.game-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    pointer-events: none;
    padding: 20px;
}

.header-left {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    pointer-events: auto;
}

.header-right {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 15px;
}

.settings-container {
    position: relative;
}

.back-btn {
    background: rgba(0, 0, 0, 0.8);
    
    border: 2px solid rgba(255, 215, 0, 0.3);
    color: #ffd700;
    padding: 12px 16px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.back-btn:hover {
    background: rgba(255, 215, 0, 0.9);
    color: #1a202c;
    border-color: #ffd700;
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
}

.player-chips {
    background: rgba(0, 0, 0, 0.8);
    
    border: 2px solid rgba(255, 215, 0, 0.3);
    padding: 12px 16px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #ffd700;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    display: none;
}

.settings-btn {
    background: rgba(0, 0, 0, 0.8);
    
    border: 2px solid rgba(255, 215, 0, 0.3);
    color: #ffd700;
    padding: 12px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.settings-btn:hover {
    background: rgba(255, 215, 0, 0.9);
    color: #1a202c;
    border-color: #ffd700;
    transform: rotate(90deg) translateY(-2px);
    box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
}

.settings-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 10px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 15px;
    padding: 10px;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
    z-index: 1002;
}

.settings-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    background: transparent;
    color: #e2e8f0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 12px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    width: 100%;
    text-align: left;
}

.dropdown-item:last-child {
    margin-bottom: 0;
}

.dropdown-item:hover {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.5);
    color: #ffd700;
    transform: translateX(5px);
}

.dropdown-item i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.game-main {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    padding: 10px;
    gap: 10px;
    z-index: 1;
}

.game-status-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1001;
    text-align: center;
}

.game-status {
    color: #ffd700;
    font-size: 1.1rem;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.8);
    
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 25px;
    padding: 8px 16px;
    display: inline-block;
    min-width: 180px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.poker-table {
    position: relative;
    background: radial-gradient(ellipse at center, #2d5a3d 0%, #1a4a2e 70%);
    border: 8px solid #8b4513;
    border-radius: 50%;
    width: calc(100vw - 40px);
    height: calc(100vh - 40px);
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        inset 0 0 50px rgba(0, 0, 0, 0.5),
        0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: visible;
}

.poker-table::before {
    content: "TEXAS HOLD'EM";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: clamp(2.5rem, 7vw, 5rem);
    font-weight: 900;
    color: rgba(255, 215, 0, 0.08);
    font-family: 'Impact', 'Trebuchet MS', 'Franklin Gothic Bold', 'Arial Black', sans-serif;
    letter-spacing: 0.15em;
    z-index: 1;
    pointer-events: none;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    user-select: none;
    text-wrap: nowrap;
}

.poker-table>* {
    position: relative;
    z-index: 2;
}

.pot-area {
    position: absolute;
    top: 55%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.pot-display {
    background: rgba(255, 215, 0, 0.2);
    border: 2px solid #ffd700;
    border-radius: 15px;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 1.2rem;
    color: #ffd700;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.community-cards {
    position: absolute;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 10px;
    z-index: 5;
}

.card-slot {
    width: 60px;
    height: 84px;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.card-slot.has-card {
    border: 2px solid rgba(255, 255, 255, 0.5);
    background: white;
    color: #333;
}

.card {
    width: 60px;
    height: 84px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.card.red {
    color: #d32f2f;
}

.card.black {
    color: #333;
}

.card-rank {
    font-size: 1.2rem;
    font-weight: bold;
    line-height: 1;
}

.card-suit {
    font-size: 1.5rem;
    line-height: 1;
}

.flying-card {
    position: absolute;
    width: 60px;
    height: 84px;
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    pointer-events: none;
}

.card-back {
    width: 60px;
    height: 84px;
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid #0d47a1;
}

.card-back {
    width: 60px;
    height: 84px;
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    border: 1px solid #0d47a1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.card-back::before {
    content: '♠';
    position: absolute;
    font-size: 2rem;
    opacity: 0.3;
}

.players-area-6p {
    position: relative;
    width: 100%;
    height: 100%;
}

.players-area {
    position: relative;
    width: 100%;
    height: 100%;
}

.player-seat {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    
    transition: all 0.3s ease;
}

.player-seat.active {
    border-color: #4caf50;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
}

.player-seat.winner {
    border-color: #ffd700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
    animation: winner-glow 2s ease-in-out infinite alternate;
}

@keyframes winner-glow {
    0% {
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
    }

    100% {
        box-shadow: 0 0 50px rgba(255, 215, 0, 1);
    }
}

.player-seat.dealer::before {
    content: 'D';
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 10;
}

.player-seat.small-blind::after {
    content: 'SB';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.7rem;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 10;
}

.player-seat.big-blind::after {
    content: 'BB';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.7rem;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 10;
}

.bottom-player {
    position: absolute;
    bottom: -4%;
    left: 50%;
    transform: translateX(-50%);
    width: 160px;
    background: rgba(76, 175, 80, 0.3);
    border: 2px solid #4caf50;
    z-index: 20;
}

.left-bottom-player {
    position: absolute;
    bottom: 5%;
    left: 8%;
    width: 140px;
}

.left-top-player {
    position: absolute;
    top: 3%;
    left: 8%;
    width: 140px;
}

.top-player {
    position: absolute;
    top: -2%;
    left: 50%;
    transform: translateX(-50%);
    width: 140px;
}

.right-top-player {
    position: absolute;
    top: 5%;
    right: 8%;
    width: 140px;
}

.right-bottom-player {
    position: absolute;
    bottom: 5%;
    right: 8%;
    width: 140px;
}

.players-area {
    position: relative;
    width: 100%;
    height: 100%;
}

.players-area.count-3 #player-0 {
    position: absolute;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    width: 140px;
}

.players-area.count-3 #player-2 {
    position: absolute;
    top: 5%;
    left: 15%;
    width: 140px;
}

.players-area.count-3 #player-3 {
    position: absolute;
    top: 5%;
    left: 50%;
    transform: translateX(-50%);
    width: 140px;
}

.players-area.count-6 #player-0 {
    position: absolute;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    width: 140px;
}

.players-area.count-6 #player-1 {
    position: absolute;
    bottom: 5%;
    left: 8%;
    width: 140px;
}

.players-area.count-6 #player-2 {
    position: absolute;
    top: 5%;
    left: 8%;
    width: 140px;
}

.players-area.count-6 #player-3 {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: 140px;
}

.players-area.count-6 #player-4 {
    position: absolute;
    top: 5%;
    right: 8%;
    width: 140px;
}

.players-area.count-6 #player-5 {
    position: absolute;
    bottom: 5%;
    right: 8%;
    width: 140px;
}

.players-area.count-9 #player-0 {
    position: absolute;
    bottom: 2%;
    left: 50%;
    transform: translateX(-50%);
    width: 130px;
}

.players-area.count-9 #player-1 {
    position: absolute;
    bottom: 15%;
    left: 8%;
    width: 130px;
}

.players-area.count-9 #player-2 {
    position: absolute;
    top: 35%;
    left: 2%;
    width: 130px;
}

.players-area.count-9 #player-3 {
    position: absolute;
    top: 2%;
    left: 50%;
    transform: translateX(-50%);
    width: 130px;
}

.players-area.count-9 #player-4 {
    position: absolute;
    top: 35%;
    right: 2%;
    width: 130px;
}

.players-area.count-9 #player-5 {
    position: absolute;
    bottom: 35%;
    right: 2%;
    width: 130px;
}

.players-area.count-9 #player-6 {
    position: absolute;
    bottom: 35%;
    left: 2%;
    width: 130px;
}

.players-area.count-9 #player-7 {
    position: absolute;
    top: 15%;
    left: 8%;
    width: 130px;
}

.players-area.count-9 #player-8 {
    position: absolute;
    top: 15%;
    right: 8%;
    width: 130px;
}

.result-modal-content {
    text-align: center;
    padding: 20px;
    max-width: 900px;
    width: 90vw;
    margin: 0 auto;
}

.result-header {
    margin-bottom: 20px;
}

.result-icon {
    font-size: 3rem;
    color: #ffd700;
    margin-bottom: 10px;
}

.result-title {
    font-size: 1.8rem;
    color: #333;
    margin: 0;
    font-weight: 600;
}

.winner-info {
    background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
    color: #333;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.winner-name {
    font-size: 1.4rem;
    font-weight: bold;
    margin-bottom: 8px;
}

.hand-type {
    font-size: 1.1rem;
    margin-bottom: 8px;
    opacity: 0.9;
}

.win-amount {
    font-size: 1.2rem;
    font-weight: bold;
}

.all-hands h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.hands-display {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.player-hand {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: white;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.player-hand.winner-hand {
    border-color: #ffd700;
    background: linear-gradient(135deg, #fff9e6 0%, #fff3cc 100%);
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.2);
}

.player-name-small {
    font-weight: bold;
    color: #333;
    min-width: 80px;
    text-align: left;
}

.player-cards-small {
    display: flex;
    gap: 5px;
}

.card-small {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.9rem;
    font-weight: bold;
    min-width: 35px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    color: #333;
    display: inline-block;
}

.card-small.red-suit {
    color: #d32f2f !important;
}

.card-small.black-suit {
    color: #333 !important;
}

.player-hand-type {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
    min-width: 80px;
    text-align: right;
}

.result-footer {
    margin-top: 25px;
    display: flex;
    justify-content: center;
    gap: 15px;
}

.continue-game-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.continue-game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.continue-game-btn:active {
    transform: translateY(0);
}

.home-btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.home-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
}

.home-btn:active {
    transform: translateY(0);
}

.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.custom-modal-overlay.show {
    opacity: 1;
}

.custom-modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 95vw;
    max-height: 85vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.custom-modal-overlay.show .custom-modal-content {
    transform: scale(1);
}

.empty-seat {
    background: rgba(255, 255, 255, 0.1);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    
}

.empty-seat-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.empty-seat-placeholder i {
    font-size: 2rem;
    margin-bottom: 8px;
    opacity: 0.5;
}

.empty-seat.filled {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #4caf50;
}

.empty-seat.filled .empty-seat-placeholder {
    display: none;
}

.empty-seat.filled .player-info {
    display: flex !important;
}

.professional-betting-panel {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 800px;
    margin: 0 auto;
}

.panel-header {
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.pot-info,
.bet-info,
.phase-info {
    text-align: center;
    flex: 1;
}

.game-info .label {
    display: block;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 4px;
}

.game-info .value {
    display: block;
    font-size: 1rem;
    font-weight: bold;
    color: #ffd700;
}

.betting-section {
    margin-bottom: 15px;
}

.action-btn {
    flex: 1;
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 0.85rem;
}

.action-btn:hover:not(:disabled) {
    border-color: #ffd700;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn i {
    font-size: 1.3rem;
}

.action-btn small {
    font-size: 0.65rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.fold-btn:hover:not(:disabled) {
    border-color: #e74c3c;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.call-btn:hover:not(:disabled) {
    border-color: #27ae60;
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.raise-btn:hover:not(:disabled) {
    border-color: #f39c12;
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
}

.bet-controls {
    display: flex;
    gap: 15px;
}

.bet-amount-section,
.quick-bet-section {
    flex: 1;
}

.bet-label {
    display: block;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
    font-weight: 500;
}

.bet-input-group {
    position: relative;
    margin-bottom: 12px;
}

.currency {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #ffd700;
    font-weight: bold;
    z-index: 2;
}

#bet-input {
    width: 100%;
    padding: 10px 10px 10px 28px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: white;
    font-size: 0.9rem;
    font-weight: bold;
}

#bet-input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
}

.bet-slider-wrapper {
    margin-top: 8px;
}

#bet-slider {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

#bet-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: #ffd700;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.quick-bet-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.quick-bet-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border: none;
    color: white;
    padding: 10px 6px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    font-size: 0.75rem;
}

.quick-bet-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.quick-bet-btn .amount {
    font-weight: bold;
    font-size: 0.8rem;
}

.quick-bet-btn small {
    font-size: 0.65rem;
    opacity: 0.9;
}

.quick-bet-btn[data-amount="all"] {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.quick-bet-btn[data-amount="all"]:hover {
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.hand-comparison-section {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.hand-comparison-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
    font-weight: 600;
}

.community-cards-display {
    margin-bottom: 15px;
    text-align: center;
}

.community-title {
    font-size: 1rem;
    color: #666;
    margin-bottom: 10px;
    font-weight: 500;
}

.community-cards-row {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 20px;
}

.community-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 2px solid #28a745;
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.2);
    color: #333 !important;
    font-weight: bold;
}

.hands-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.player-comparison {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 10px;
    transition: all 0.3s ease;
}

.player-comparison.winner-comparison {
    border-color: #ffd700;
    background: linear-gradient(135deg, #fff9e6 0%, #fff3cc 100%);
    box-shadow: 0 5px 20px rgba(255, 215, 0, 0.3);
    transform: scale(1.02);
}

.player-name-comparison {
    font-size: 1.1rem;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.winner-crown {
    color: #ffd700;
    font-size: 1.2rem;
    animation: bounce 2s infinite;
}

.player-cards-comparison {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.player-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 2px solid #007bff;
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.2);
    color: #333 !important;
    font-weight: bold;
}

.hand-result {
    margin-left: auto;
    text-align: right;
}

.hand-type-name {
    font-size: 1.1rem;
    font-weight: bold;
    color: #28a745;
    margin-bottom: 4px;
}

.hand-strength {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

.fold-indicator {
    color: #dc3545;
    font-style: italic;
    font-size: 1rem;
    opacity: 0.8;
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-5px);
    }

    60% {
        transform: translateY(-3px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .result-modal-content {
        padding: 10px;
        max-width: 95vw;
        max-height: 90vh;
    }

    .result-header {
        margin-bottom: 12px;
    }

    .result-body {
        margin-bottom: 15px;
    }

    .winner-info {
        padding: 12px;
        margin-bottom: 12px;
    }

    .hand-comparison-section {
        margin-top: 10px;
    }

    .community-cards-display {
        margin-bottom: 10px;
    }

    .community-title {
        font-size: 0.9rem;
        margin-bottom: 6px;
    }

    .community-cards-row {
        gap: 4px;
    }

    .hands-comparison {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .player-comparison {
        padding: 8px;
        margin-bottom: 6px;
    }

    .player-comparison-header {
        margin-bottom: 6px;
    }

    .player-name-comparison {
        font-size: 0.9rem;
    }

    .player-cards-comparison {
        gap: 6px;
        margin-bottom: 6px;
    }

    .card-small {
        width: 28px;
        height: 40px;
        font-size: 0.7rem;
        padding: 2px;
    }

    .hand-result {
        gap: 4px;
    }

    .hand-type-name {
        font-size: 0.8rem;
    }

    .hand-strength {
        font-size: 0.7rem;
    }

    .result-footer {
        flex-direction: row;
        gap: 8px;
        padding: 10px;
    }

    .continue-game-btn,
    .home-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
        flex: 1;
    }

    .result-icon {
        font-size: 1.8rem;
    }

    .result-title {
        font-size: 1.2rem;
        margin: 6px 0;
    }

    .winner-name {
        font-size: 1.1rem;
        margin-bottom: 4px;
    }

    .hand-type {
        font-size: 0.9rem;
        margin-bottom: 4px;
    }

    .win-amount {
        font-size: 1rem;
        font-weight: bold;
    }

    .bet-controls {
        flex-direction: column;
        gap: 15px;
    }

    .action-buttons {
        gap: 10px;
    }

    .quick-bet-buttons {
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }
}

@media (max-width: 480px) {
    .result-modal-content {
        padding: 8px;
        max-width: 98vw;
    }

    .winner-info {
        padding: 10px;
    }

    .player-comparison {
        padding: 6px;
    }

    .card-small {
        width: 24px;
        height: 34px;
        font-size: 0.6rem;
    }

    .result-title {
        font-size: 1.1rem;
    }

    .winner-name {
        font-size: 1rem;
    }

    .continue-game-btn,
    .home-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .community-cards-row {
        gap: 3px;
    }

    .hands-comparison {
        gap: 6px;
    }
}

.player-seat.folded {
    opacity: 0.5;
    filter: grayscale(100%);
}

.player-seat#player-0 {
    bottom: -4%;
    left: 50%;
    transform: translateX(-50%);
}

.player-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.player-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.player-details {
    text-align: left;
}

.player-name {
    font-weight: 600;
    font-size: 0.8rem;
    margin-bottom: 2px;
}

.player-chips-display {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    color: #ffd700;
}

.player-cards {
    display: flex;
    gap: 5px;
}

.player-action {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: bold;
    white-space: nowrap;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 15;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
    border: 2px solid #fff;
    min-width: 60px;
    text-align: center;
}

.player-action.show {
    opacity: 1;
    transform: scale(1.05);
}

.player-action::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 8px solid transparent;
}

.bottom-player .player-action {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 10px;
}

.bottom-player .player-action::after {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: rgba(255, 255, 255, 0.95);
}

.top-player .player-action {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 10px;
}

.top-player .player-action::after {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: rgba(255, 255, 255, 0.95);
}

.left-top-player .player-action,
.left-bottom-player .player-action {
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 10px;
}

.left-top-player .player-action::after,
.left-bottom-player .player-action::after {
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-right-color: rgba(255, 255, 255, 0.95);
}

.right-top-player .player-action,
.right-bottom-player .player-action {
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-right: 10px;
}

.right-top-player .player-action::after,
.right-bottom-player .player-action::after {
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: rgba(255, 255, 255, 0.95);
}

.player-bet {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid #ffd700;
    border-radius: 10px;
    padding: 5px 10px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #ffd700;
    min-width: 60px;
    text-align: center;
    display: none;
}

.dealer-button {
    position: absolute;
    width: 30px;
    height: 30px;
    background: #ffd700;
    border: 2px solid #ffb300;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #333;
    font-size: 0.9rem;
    box-shadow: 0 3px 10px rgba(255, 215, 0, 0.5);
    transition: all 0.3s ease;
}

.control-panel {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 5px;
    padding: 12px;
    z-index: 100;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 220px;
}

.control-panel.hidden {
    opacity: 0;
    pointer-events: none;
    transform: translateX(-50%) translateY(10px);
}

.bet-slider-container {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bet-slider-label {
    color: #ecf0f1;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

.action-buttons {
    display: flex;
    gap: 10px;
    overflow: hidden;
}

.action-btn {
    flex: 1;
    padding: 5px 12px;
    border: none;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:last-child {
    border-right: none;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.fold-btn {
    background: #e74c3c;
}

.call-btn {
    background: #3498db;
}

.raise-btn {
    background: #e67e22;
}

#bet-slider {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
}

#bet-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#bet-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bet-amount-display {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 4px 8px;
    color: #f39c12;
    font-weight: 600;
    font-size: 12px;
    min-width: 50px;
    text-align: center;
}

.game-controls {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    gap: 15px;
    z-index: 10;
}

.game-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.game-status {
    position: fixed;
    top: 60%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 10px 20px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
    display: none;
}

@media (max-width: 1024px) {
    .poker-table {
        min-height: 400px;
    }

    .player-avatar {
        width: 30px;
        height: 30px;
    }

    .card,
    .card-back,
    .card-slot {
        width: 38px;
        height: 50px;
    }


    .community-cards {
        gap: 8px;
        top: 42%;
    }

    .pot-area {
        top: 50%;
    }

    .pot-display {
        padding: 3px 9px;
    }

    .player-cards {
        gap: 3px;
    }

    .player-seat.dealer::before,
    .player-seat.small-blind::after,
    .player-seat.big-blind::after {
        width: 25px;
        height: 25px;
        line-height: 25px;
    }

    .control-panel {
        padding: 0 0 5px;
        gap: 5px;
    }

    .bet-slider-container {
        padding: 0;
        background: none;
        border: none;

    }

    .action-btn {
        padding: 5px;
    }

    .settings-btn {
        padding: 5px;
        width: 40px;
        height: 40px;
    }

    .header-left {
        top: 10px;
        left: 10px;
    }

    .header-right {
        top: 10px;
        right: 10px;
    }

    .back-btn {
        padding: 5px;
    }
    #player-0-9p,.player-seat#player-0,.bottom-player{
        bottom: -6%;
    }
    .result-header,.winner-info{
        display: none;
    }
    .hand-comparison-section{
        background: none;
        border: none;
        padding: 0;
        margin: 0;
    }
    
    .hand-comparison-section h3{
        margin-bottom: 0;
    }

    .community-cards-row{
        margin-bottom: 10px;
    }

    .result-footer{
        margin-top: 10px;
    }
    
    .top-player{
        top: -4%;
    }
}

@media (max-width: 768px) {
    .game-header {
        padding: 15px 20px;
    }

    .game-title {
        font-size: 1.4rem;
    }

    .poker-table {
        min-height: 400px;
        border-width: 4px;
    }

    .card,
    .card-back,
    .card-slot {
        width: 35px;
        height: 49px;
        font-size: 0.6rem;
    }

    .community-cards {
        gap: 4px;
    }

    .player-seat {
        padding: 8px;
        width: 120px !important;
    }

    .player-avatar {
        width: 25px;
        height: 25px;
        font-size: 0.8rem;
    }

    .player-name {
        font-size: 0.7rem;
    }

    .player-chips-display {
        font-size: 0.6rem;
    }

    .chips-amount {
        font-size: 0.6rem;
    }

    .bottom-player {
        bottom: 5px;
        width: 140px !important;
    }

    .left-bottom-player {
        bottom: 20%;
        left: 2%;
        width: 110px !important;
    }

    .left-top-player {
        top: 15%;
        left: 2%;
        width: 110px !important;
    }

    .top-player {
        top: 5px;
        width: 120px !important;
    }

    .right-top-player {
        top: 15%;
        right: 2%;
        width: 110px !important;
    }

    .right-bottom-player {
        bottom: 20%;
        right: 2%;
        width: 110px !important;
    }

    .control-panel {
        min-width: 180px;
        padding: 8px;
        gap: 6px;
        margin-bottom: 10px;
    }

    .bet-slider-container {
        padding: 6px 8px;
        gap: 6px;
    }

    .bet-slider-label {
        font-size: 10px;
    }

    .bet-amount-display {
        font-size: 10px;
        min-width: 40px;
        padding: 3px 6px;
    }

    .action-btn {
        padding: 8px 6px;
        font-size: 12px;
        margin: 10px 0;
        text-wrap: nowrap;
    }
}

@media (max-width: 480px) {
    .game-main {
        padding: 5px;
    }

    .poker-table {
        min-height: 350px;
    }

    .card,
    .card-back,
    .card-slot {
        width: 35px;
        height: 50px;
        font-size: 0.5rem;
    }

    .community-cards {
        gap: 3px;
    }

    .player-seat {
        padding: 6px;
        width: 100px !important;
    }

    .player-avatar {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
    }

    .player-name {
        font-size: 0.6rem;
    }

    .player-chips-display {
        font-size: 0.5rem;
    }

    .bottom-player {
        bottom: 3px;
        width: 120px !important;
    }

    .left-bottom-player {
        bottom: 25%;
        left: 1%;
        width: 95px !important;
    }

    .left-top-player {
        top: 20%;
        left: 1%;
        width: 95px !important;
    }

    .top-player {
        top: 3px;
        width: 100px !important;
    }

    .right-top-player {
        top: 20%;
        right: 1%;
        width: 95px !important;
    }

    .right-bottom-player {
        bottom: 25%;
        right: 1%;
        width: 95px !important;
    }

    .control-panel {
        min-width: 160px;
        padding: 6px;
        gap: 4px;
        margin-bottom: 8px;
    }

    .bet-slider-container {
        padding: 4px 6px;
        gap: 4px;
    }

    .bet-slider-label {
        font-size: 9px;
    }

    .bet-amount-display {
        font-size: 9px;
        min-width: 35px;
        padding: 2px 4px;
    }

    .pot-display {
        font-size: 0.8rem;
    }

    .game-controls {
        transform: translate(-50%, -50%) scale(0.9);
    }
}

@media (max-width: 768px) and (orientation: landscape) {
    .poker-table {
        min-height: 280px;
    }

    .left-bottom-player {
        bottom: 15%;
        left: 3%;
    }

    .left-top-player {
        top: 10%;
        left: 3%;
    }

    .right-top-player {
        top: 10%;
        right: 3%;
    }

    .right-bottom-player {
        bottom: 15%;
        right: 3%;
    }

    .top-player {
        top: 2px;
    }

    .bottom-player {
        bottom: 2px;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .player-seat {
        width: 110px;
        padding: 5px;
    }

    .left-bottom-player,
    .left-top-player {
        left: 5%;
    }

    .right-bottom-player,
    .right-top-player {
        right: 5%;
    }
}

.player-count-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.player-count-modal.show {
    opacity: 1;
    visibility: visible;
}

.player-count-modal .modal-content {
    background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(255, 215, 0, 0.3);
    max-width: 600px;
    width: 90%;
}

.player-count-modal .modal-header h3 {
    color: #ffd700;
    text-align: center;
    margin: 0 0 30px 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.player-count-options {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.count-option {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border: 2px solid rgba(255, 215, 0, 0.2);
    border-radius: 15px;
    padding: 25px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    flex: 1;
    max-width: 180px;
}

.count-option:hover {
    border-color: #ffd700;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
}

.count-option .count-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.count-option .count-title {
    color: #ffd700;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.count-option .count-desc {
    color: #a0aec0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.left-top-player-3p {
    position: absolute;
    top: 0%;
    left: 15%;
    width: 140px;
}

.right-top-player-3p {
    position: absolute;
    top: 0%;
    right: 15%;
    width: 140px;
}

#player-0-9p {
    position: absolute;
    bottom: -4%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(76, 175, 80, 0.3);
    border: 2px solid #4caf50;
}

#player-1-9p {
    position: absolute;
    bottom: 0%;
    left: 20%;
}

#player-2-9p {
    position: absolute;
    top: 40%;
    left: 0%;
}

#player-3-9p {
    position: absolute;
    top: 4%;
    left: 10%;
}

#player-4-9p {
    position: absolute;
    top: -2%;
    left: 39%;
    transform: translateX(-50%);
}

#player-5-9p {
    position: absolute;
    top: -2%;
    right: 33%;
}

#player-6-9p {
    position: absolute;
    top: 4%;
    right: 10%;
}

#player-7-9p {
    position: absolute;
    top: 40%;
    right: 0%;
}

#player-8-9p {
    position: absolute;
    bottom: 0%;
    right: 20%;
}

.rotate-prompt {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: #ffd700;
    text-align: center;
    padding: 20px;
}

.rotate-prompt .rotate-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: rotate 2s linear infinite;
}

.rotate-prompt h2 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.rotate-prompt p {
    font-size: 1rem;
    opacity: 0.8;
    line-height: 1.5;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

@media (max-width: 768px) and (orientation: portrait) {
    .game-container {
        display: none !important;
    }

    .rotate-prompt {
        display: flex !important;
    }
}

/* 移动端竖屏时默认显示横屏提示，避免扑克牌闪现 */
@media screen and (max-width: 768px) {
    .rotate-prompt {
        display: flex;
    }

    .game-container {
        display: none;
    }
}

/* 移动端横屏时显示游戏内容 */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .rotate-prompt {
        display: none !important;
    }

    .game-container {
        display: flex !important;
    }
}

@media (max-width: 768px) and (orientation: landscape) {
    .poker-table {
        width: calc(100vw - 20px) !important;
        height: calc(100vh - 20px) !important;
        max-width: calc(100vh - 20px) !important;
        min-height: auto !important;
        border-width: 4px;
    }

    .player-seat {
        width: 70px !important;
        padding: 4px !important;
        transform: scale(0.7);
        border-radius: 8px !important;
    }

    .bottom-player {
        bottom: 3% !important;
        width: 70px !important;
    }

    .left-bottom-player {
        bottom: 18% !important;
        left: 3% !important;
        width: 70px !important;
    }

    .left-top-player {
        top: 8% !important;
        left: 8% !important;
        width: 70px !important;
    }

    .top-player {
        top: -20px !important;
        width: 70px !important;
    }

    .right-top-player {
        top: 8% !important;
        right: 8% !important;
        width: 70px !important;
    }

    .right-bottom-player {
        bottom: 18% !important;
        right: 3% !important;
        width: 70px !important;
    }

    .left-top-player-3p {
        top: 8% !important;
        left: 12% !important;
        width: 70px !important;
    }

    .right-top-player-3p {
        top: 8% !important;
        right: 12% !important;
        width: 70px !important;
    }

    #player-1-9p {
        bottom: 18% !important;
        left: 4% !important;
        width: 65px !important;
    }

    #player-2-9p {
        top: 35% !important;
        left: 1% !important;
        width: 65px !important;
    }

    #player-3-9p {
        top: 12% !important;
        left: 12% !important;
        width: 65px !important;
    }

    #player-4-9p {
        top: -20px !important;
        width: 65px !important;
    }

    #player-5-9p {
        top: 12% !important;
        right: 12% !important;
        width: 65px !important;
    }

    #player-6-9p {
        top: 35% !important;
        right: 1% !important;
        width: 65px !important;
    }

    #player-7-9p {
        bottom: 18% !important;
        right: 4% !important;
        width: 65px !important;
    }

    #player-8-9p {
        bottom: 3% !important;
        right: 18% !important;
        width: 65px !important;
    }

    .card-slot {
        width: 25px !important;
        height: 35px !important;
        font-size: 0.5rem !important;
    }

    .player-name {
        font-size: 0.6rem !important;
    }

    .chips-amount {
        font-size: 0.5rem !important;
    }

    .bet-amount {
        font-size: 0.6rem !important;
    }

    .community-cards .card-slot {
        width: 30px !important;
        height: 42px !important;
        font-size: 0.6rem !important;
    }

    .control-panel {
        min-width: 160px !important;
        padding: 6px !important;
        gap: 4px !important;
        margin-bottom: 8px !important;
        transform: scale(0.9);
    }

    .back-btn,
    .player-chips,
    .settings-btn {
        padding: 8px 12px !important;
        font-size: 0.8rem !important;
        transform: scale(0.9);
    }

    .game-status {
        font-size: 0.9rem !important;
        padding: 6px 12px !important;
        min-width: 140px !important;
        transform: scale(0.9);
    }

    .poker-table::before {
        font-size: clamp(2rem, 6vw, 3.5rem) !important;
    }

    .player-avatar {
        width: 18px !important;
        height: 18px !important;
        font-size: 0.6rem !important;
    }

    .player-info {
        gap: 3px !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .player-details {
        text-align: center !important;
    }

    .player-chips-display {
        font-size: 0.5rem !important;
    }

    .player-bet {
        padding: 2px 6px !important;
        font-size: 0.5rem !important;
        min-width: 30px !important;
    }

    /* Mobile bubble style adjustment - ensure bubbles display on player cards */
    .player-action {
        font-size: 0.6rem !important;
        padding: 4px 8px !important;
        border-radius: 12px !important;
        z-index: 1000 !important;
    }

    /* Bottom player bubble */
    .bottom-player .player-action {
        bottom: 100% !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        margin-bottom: 5px !important;
    }

    /* Top player bubble */
    .top-player .player-action {
        top: 100% !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        margin-top: 5px !important;
    }

    /* Left side player bubbles */
    .left-top-player .player-action,
    .left-bottom-player .player-action {
        left: 100% !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        margin-left: 5px !important;
    }

    /* Right side player bubbles */
    .right-top-player .player-action,
    .right-bottom-player .player-action {
        right: 100% !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        margin-right: 5px !important;
    }

    /* Three-player table special position bubbles */
    .left-top-player-3p .player-action {
        left: 100% !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        margin-left: 5px !important;
    }

    .right-top-player-3p .player-action {
        right: 100% !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        margin-right: 5px !important;
    }

    /* Nine-player table special position bubbles */
    .left-middle-player-9p .player-action {
        left: 100% !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        margin-left: 5px !important;
    }

    .right-middle-player-9p .player-action {
        right: 100% !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        margin-right: 5px !important;
    }

    .bottom-right-player-9p .player-action {
        bottom: 100% !important;
        right: 0% !important;
        transform: translateY(0%) !important;
        margin-bottom: 5px !important;
    }

    /* Bubble arrow adjustment */
    .player-action::after {
        border-width: 4px !important;
    }
}