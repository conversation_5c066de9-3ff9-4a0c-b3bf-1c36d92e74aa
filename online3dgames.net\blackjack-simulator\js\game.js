function checkIOSDevice() {
    const userAgent = navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(userAgent) ||
                 (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    const isStandalone = window.navigator.standalone === true;

    return isIOS && !isStandalone;
}

function showIOSTip() {
    if (checkIOSDevice()) {
        $('#ios-fullscreen-tip').show();
    }
}



$(document).ready(function() {
    // Remove Google Fonts dependencies for offline compatibility
    $('link[href*="fonts.googleapis.com"], link[href*="fonts.gstatic.com"]').remove();

    // Update font family to use system fonts
    $('body').css('font-family', '-apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif');

    // Update viewport for full screen coverage including notch areas
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover');
    }

    // Add CSS for safe area support and full screen coverage
    const safeAreaCSS = `
        <style id="safe-area-styles">
            /* Full screen coverage including notch areas */
            html {
                width: 100vw;
                height: 100vh;
                height: 100dvh; /* Dynamic viewport height for mobile */
                overflow-x: hidden;
                margin: 0;
                padding: 0;
            }

            body {
                min-height: 100vh;
                min-height: 100dvh;
                width: 100vw;
                overflow-x: hidden;
                margin: 0;
                padding: 0;
                /* Force background to extend to all edges including notch areas */
                background-attachment: fixed;
                background-size: cover !important;
                background-position: center center !important;
                /* Ensure body covers entire viewport including unsafe areas */
                position: relative;
            }

            /* Force full coverage on iOS Safari */
            @media screen and (-webkit-min-device-pixel-ratio: 2) {
                html, body {
                    width: 100vw;
                    min-height: 100vh;
                    min-height: 100dvh;
                    margin: 0 !important;
                    padding: 0 !important;
                }
            }

            /* Safe area support for notch and punch-hole displays */
            @supports (padding: max(0px)) {
                .game-container {
                    /* Add padding for safe areas to main content */
                    padding-left: max(8px, env(safe-area-inset-left));
                    padding-right: max(8px, env(safe-area-inset-right));
                    padding-top: max(8px, env(safe-area-inset-top));
                    padding-bottom: max(8px, env(safe-area-inset-bottom));
                }

                .game-controls {
                    /* Ensure controls respect safe areas */
                    padding-left: max(8px, env(safe-area-inset-left));
                    padding-right: max(8px, env(safe-area-inset-right));
                }

                .top-controls {
                    /* Top controls with safe area padding */
                    padding-left: max(8px, env(safe-area-inset-left));
                    padding-right: max(8px, env(safe-area-inset-right));
                    padding-top: max(8px, env(safe-area-inset-top));
                }
            }

            /* Fallback for older browsers */
            @media screen and (max-width: 430px) {
                .game-container {
                    padding-left: 8px;
                    padding-right: 8px;
                }

                .top-controls {
                    padding-left: 8px;
                    padding-right: 8px;
                    padding-top: 8px;
                }
            }

            /* Additional mobile optimizations */
            @media screen and (orientation: landscape) and (max-height: 500px) {
                /* Landscape mode on phones - ensure full coverage */
                html, body {
                    width: 100vw !important;
                    height: 100vh !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                .game-container {
                    width: 100vw;
                    height: 100vh;
                    padding: 0;
                }
            }

            /* iOS specific fixes */
            @supports (-webkit-touch-callout: none) {
                /* iOS Safari specific */
                html {
                    -webkit-text-size-adjust: 100%;
                    -webkit-touch-callout: none;
                    -webkit-user-select: none;
                }

                body {
                    -webkit-overflow-scrolling: touch;
                    /* Prevent iOS Safari from adding margins */
                    margin: 0 !important;
                    padding: 0 !important;
                }
            }
        </style>
    `;

    $('head').append(safeAreaCSS);

    // Debug safe area values (remove in production)
    if (window.CSS && window.CSS.supports && window.CSS.supports('padding', 'env(safe-area-inset-top)')) {
        console.log('Safe area support detected');
        // Add visual indicators for safe areas in development
        const debugCSS = `
            <style id="debug-safe-area">
                body::before {
                    content: '';
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: env(safe-area-inset-top, 0px);
                    background: rgba(255, 0, 0, 0.1);
                    z-index: 9999;
                    pointer-events: none;
                }
                body::after {
                    content: '';
                    position: fixed;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: env(safe-area-inset-bottom, 0px);
                    background: rgba(0, 255, 0, 0.1);
                    z-index: 9999;
                    pointer-events: none;
                }
            </style>
        `;
        // Uncomment next line for debugging safe areas
        // $('head').append(debugCSS);
    }

    // Handle mobile fullscreen coverage only when in actual fullscreen mode
    function ensureFullScreenCoverage() {
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isFullscreen = !!(document.fullscreenElement ||
                               document.webkitFullscreenElement ||
                               document.mozFullScreenElement ||
                               document.msFullscreenElement);

        if (isMobile && isFullscreen) {
            // Force viewport dimensions only in fullscreen
            $('html, body').css({
                'width': '100vw',
                'height': '100vh',
                'margin': '0',
                'padding': '0',
                'overflow-x': 'hidden'
            });

            // For iOS, handle the status bar area
            if (isIOS) {
                // Check if running in standalone mode (added to home screen)
                const isStandalone = window.navigator.standalone || window.matchMedia('(display-mode: standalone)').matches;

                if (isStandalone) {
                    // In standalone mode, we can use the full screen
                    $('body').css({
                        'padding-top': '0',
                        'height': '100vh'
                    });
                } else {
                    // In browser mode, account for Safari UI
                    $('body').css({
                        'height': '100vh',
                        'min-height': '-webkit-fill-available'
                    });
                }
            }
        } else if (isMobile && !isFullscreen) {
            // Reset styles when not in fullscreen to allow normal scrolling
            $('html, body').css({
                'width': '',
                'height': '',
                'margin': '',
                'padding': '',
                'overflow-x': ''
            });

            $('body').css({
                'padding-top': '',
                'height': '',
                'min-height': ''
            });
        }

        // Update viewport meta tag
        if (isMobile) {
            let viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover, minimal-ui');
            }
        }
    }

    showIOSTip();

    const domCache = {
        $body: $('body'),
        $document: $(document),
        $window: $(window),
        $gameStatus: null,
        $bettingControls: null,
        $actionControls: null,
        $dealCards: null,
        $balance: null,
        $deckCount: null,
        $chipButtons: null,
        $clearBet: null,
        $allIn: null,
        $playerBalanceElements: null,
        $betCircles: null,
        $playerPositions: null,
        $dealerCardsArea: null,
        $playerCardsAreas: null,
        $actionButtons: null,
        $sideBetControls: null,
        $rulesModal: null,
        $gameOverModal: null,
        $insurancePanel: null
    };
    const activeTimers = {
        timeouts: new Set(),
        intervals: new Set(),
        animationFrames: new Set(),
        maxTimers: 100 // 限制最大定时器数量
    };

    function safeSetTimeout(callback, delay) {
        // 检查定时器数量限制
        if (activeTimers.timeouts.size >= activeTimers.maxTimers) {
            console.warn('Too many active timeouts, clearing old ones');
            clearOldTimers();
        }

        const timeoutId = setTimeout(() => {
            activeTimers.timeouts.delete(timeoutId);
            try {
                callback();
            } catch (error) {
                console.error('Error in timeout callback:', error);
            }
        }, delay);
        activeTimers.timeouts.add(timeoutId);
        return timeoutId;
    }

    function safeSetInterval(callback, delay) {
        if (activeTimers.intervals.size >= 20) { // 间隔定时器限制更严格
            console.warn('Too many active intervals, clearing old ones');
            clearOldTimers();
        }

        const intervalId = setInterval(() => {
            try {
                callback();
            } catch (error) {
                console.error('Error in interval callback:', error);
                safeClearInterval(intervalId);
            }
        }, delay);
        activeTimers.intervals.add(intervalId);
        return intervalId;
    }

    function safeClearTimeout(timeoutId) {
        if (timeoutId) {
            clearTimeout(timeoutId);
            activeTimers.timeouts.delete(timeoutId);
        }
    }

    function safeClearInterval(intervalId) {
        if (intervalId) {
            clearInterval(intervalId);
            activeTimers.intervals.delete(intervalId);
        }
    }

    function safeRequestAnimationFrame(callback) {
        const frameId = requestAnimationFrame(() => {
            activeTimers.animationFrames.delete(frameId);
            try {
                callback();
            } catch (error) {
                console.error('Error in animation frame callback:', error);
            }
        });
        activeTimers.animationFrames.add(frameId);
        return frameId;
    }

    function clearOldTimers() {
        // 清理一半的旧定时器
        const timeoutsArray = Array.from(activeTimers.timeouts);
        const halfLength = Math.floor(timeoutsArray.length / 2);
        for (let i = 0; i < halfLength; i++) {
            clearTimeout(timeoutsArray[i]);
            activeTimers.timeouts.delete(timeoutsArray[i]);
        }
    }

    function clearAllTimers() {
        activeTimers.timeouts.forEach(id => clearTimeout(id));
        activeTimers.intervals.forEach(id => clearInterval(id));
        activeTimers.animationFrames.forEach(id => cancelAnimationFrame(id));
        activeTimers.timeouts.clear();
        activeTimers.intervals.clear();
        activeTimers.animationFrames.clear();
    }
    let animationCleanupScheduled = false;

    function cleanupAnimationElements() {
        if (animationCleanupScheduled) {
            return;
        }
        animationCleanupScheduled = true;

        safeSetTimeout(() => {
            try {
                // Remove all flying chips
                $('.flying-chip').remove();

                // Clean up action bubbles
                $('.action-bubble').removeClass('show hit stand bust');

                // Remove animation classes more efficiently
                const animatedElements = $('[class*="animate-"]');
                animatedElements.each(function() {
                    const element = $(this);
                    const className = element.attr('class');
                    if (className) {
                        const cleanedClass = className.replace(/(^|\s)animate-\S+/g, '').trim();
                        element.attr('class', cleanedClass);
                    }
                });

                // Clean up orphaned chip stacks
                $('.bet-chips-stack, .side-bet-chips-stack').each(function() {
                    const $this = $(this);
                    if ($this.children().length === 0 || $this.is(':empty')) {
                        $this.remove();
                    }
                });

                // Clean up split hand containers
                $('.split-hands-container, .split-hands-final-container').each(function() {
                    const $this = $(this);
                    if ($this.children().length === 0) {
                        $this.remove();
                    }
                });

                // Clean up temporary elements
                $('.splitting-card, .mini-card, .temp-card').remove();

                // Clean up any detached DOM elements
                $('*').filter(function() {
                    return !$.contains(document, this);
                }).remove();

                animationCleanupScheduled = false;

                // Force garbage collection hint
                if (window.gc && typeof window.gc === 'function') {
                    window.gc();
                }
            } catch (error) {
                console.error('Error during animation cleanup:', error);
                animationCleanupScheduled = false;
            }
        }, 100);
    }

    function scheduleAnimationCleanup() {
        if (!animationCleanupScheduled) {
            safeSetTimeout(cleanupAnimationElements, 1000);
        }
    }
    function initDOMCache() {
        domCache.$gameStatus = $('#game-status');
        domCache.$bettingControls = $('#betting-controls');
        domCache.$actionControls = $('#action-controls');
        domCache.$dealCards = $('#deal-cards');
        domCache.$deckCount = $('#deck-count');
        domCache.$bettingCountdown = $('#betting-countdown');
        domCache.$countdownTimer = $('#countdown-timer');
        domCache.$rulesModal = $('#rules-modal');
        domCache.$sideBetControls = $('#side-bet-controls');
        domCache.$clearBet = $('#clear-bet');
        domCache.$allIn = $('#all-in');
        domCache.$playerBalanceElements = $('[id^="player-balance"]');
        domCache.$betCircles = $('.bet-circle');
        domCache.$playerPositions = $('.player-position');
        domCache.$dealerCardsArea = $('.dealer-cards-area');
        domCache.$playerCardsAreas = $('[id^="player-cards"]');
        domCache.$actionButtons = $('#hit, #stand, #double-down, #split');
        domCache.$gameOverModal = $('#game-over-modal');
        domCache.$insurancePanel = $('#insurance-panel');
    }

    function refreshDOMCache() {
        domCache.$chipButtons = $('.chip-btn');
        domCache.$betCircles = $('.bet-circle');
        domCache.$playerPositions = $('.player-position');
    }

    function preloadChipImages() {
        const chipImages = [
            '/blackjack-simulator/images/chips/chips1.png',
            '/blackjack-simulator/images/chips/chips2.png',
            '/blackjack-simulator/images/chips/chips3.png',
            '/blackjack-simulator/images/chips/chips4.png',
            '/blackjack-simulator/images/chips/chips5.png',
            '/blackjack-simulator/images/chips/chips1-preview.svg',
            '/blackjack-simulator/images/chips/chips2-preview.svg',
            '/blackjack-simulator/images/chips/chips3-preview.svg',
            '/blackjack-simulator/images/chips/chips4-preview.svg',
            '/blackjack-simulator/images/chips/chips5-preview.svg'
        ];

        chipImages.forEach(src => {
            const img = new Image();
            img.src = src;
            img.onerror = () => {
                console.warn(`Failed to load chip image: ${src}`);
            };
        });
    }
    let gameSettings = {
        playerCount: 3,
        deckCount: 6,
        availableAvatars: ['/blackjack-simulator/images/player1.png', '/blackjack-simulator/images/player2.png', '/blackjack-simulator/images/player3.png', '/blackjack-simulator/images/player4.png', '/blackjack-simulator/images/player5.png']
    };

    function getPlayerHtmlPosition(playerIndex) {
        if (gameSettings.playerCount === 1) {
            return 1;
        } else if (gameSettings.playerCount === 3) {
            return [0, 1, 2][playerIndex];
        } else if (gameSettings.playerCount === 4) {
            return [0, 1, 2, 3][playerIndex];
        } else if (gameSettings.playerCount === 6) {
            return [0, 1, 2, 3, 4, 5][playerIndex];
        }
        return playerIndex;
    }
    let gameState = {
        deck: [],
        discardPile: [],
        totalCards: 0,
        dealerCards: [],
        dealerScore: 0,
        players: [],
        currentPlayerIndex: 1,
        balance: 1000,
        balanceCache: 1000,
        gameInProgress: false,
        dealerHiddenCard: null,
        gameHistory: [],
        soundEnabled: true,
        currentTurnIndex: -1,
        gamePhase: 'waiting',
        gameStarted: false,
        isShuffling: false,
        bettingCountdown: 15,
        countdownTimer: null,
        countdownActive: false,
        gameOverModalShown: false,
        achievements: {
            highestWin: 0,
            currentWinStreak: 0,
            bestWinStreak: 0,
            totalGames: 0,
            totalWins: 0,
            startingBalance: 1000,
            sessionStartBalance: 1000,
            sessionStartBalanceCache: 1000
        },
        aiBettingSchedule: [],
        playerReady: false,
        autoStartScheduled: false,
        hasAutoFullscreened: false,
        insuranceOffered: false
    };
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const suitColors = {'♠': 'black', '♣': 'black', '♥': 'red', '♦': 'red'};
    function initializePlayers() {
        gameState.players = [];
        const shuffledAvatars = [...gameSettings.availableAvatars].sort(() => Math.random() - 0.5);
        let avatarIndex = 0;
        const humanPlayerIndex = gameSettings.playerCount === 1 ? 0 : Math.floor(gameSettings.playerCount / 2);
        for (let i = 0; i < gameSettings.playerCount; i++) {
            const isHuman = i === humanPlayerIndex;
            const player = {
                cards: [],
                score: 0,
                bet: 0,
                isActive: false,
                isAI: !isHuman,
                name: isHuman ? 'You' : `Player ${i + 1}`,
                isBust: false,
                avatar: isHuman ? '/blackjack-simulator/images/user.png' : shuffledAvatars[avatarIndex++ % shuffledAvatars.length],
                splitHands: [],
                currentHandIndex: 0,
                canSplit: false,
                sideBets: {
                    perfectPairs: 0,
                    twentyOnePlusThree: 0
                },
                hasInsurance: false,
                insuranceBet: 0
            };
            gameState.players.push(player);
        }
        gameState.currentPlayerIndex = humanPlayerIndex;
    }
    function updatePlayerPositionsDisplay() {
        $('.player-position').hide();
        $('.players-area').removeClass('player-count-1 player-count-3 player-count-4 player-count-6');
        $('.players-area').addClass(`player-count-${gameSettings.playerCount}`);
        const humanPlayerIndex = gameSettings.playerCount === 1 ? 0 : Math.floor(gameSettings.playerCount / 2);

        for (let i = 0; i < gameSettings.playerCount; i++) {
            const htmlPosition = getPlayerHtmlPosition(i);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            if (playerPosition.length > 0) {
                playerPosition.show();
                const player = gameState.players[i];
                if (player) {
                    const avatarImg = playerPosition.find('.player-avatar img');
                    const playerName = playerPosition.find('.player-name');
                    if (avatarImg.length > 0) {
                        avatarImg.attr('src', player.avatar);
                        avatarImg.attr('alt', player.name);
                    }
                    if (playerName.length > 0) {
                        playerName.text(player.name);
                    }
                    if (i === humanPlayerIndex) {
                        playerPosition.addClass('current-player');
                        const playerBalance = playerPosition.find('.player-balance');
                        if (playerBalance.length > 0) {
                            playerBalance.show();
                        }
                    } else {
                        playerPosition.removeClass('current-player');
                        const playerBalance = playerPosition.find('.player-balance');
                        if (playerBalance.length > 0) {
                            playerBalance.hide();
                        }
                    }
                }
            }
        }
    }
    function initGame() {
        initializePlayers(); 
        createDeck();
        shuffleDeck();
        updateDisplay();
        hideAllPlayerScores(); 
        updateChipDenominations();
        bindEvents();
        bindSettingsEvents();
    }
    function createDeck() {
        gameState.deck = [];
        gameState.discardPile = [];
        for (let deckNum = 0; deckNum < gameSettings.deckCount; deckNum++) {
            for (let suit of suits) {
                for (let value of values) {
                    gameState.deck.push({
                        suit: suit,
                        value: value,
                        color: suitColors[suit]
                    });
                }
            }
        }
        gameState.totalCards = gameState.deck.length;
        updateDeckDisplay();
    }
    function updateDeckDisplay() {
        const remainingCards = gameState.deck.length;
        const discardedCards = gameState.discardPile.length;
        const totalCards = gameState.totalCards;
        $('#deck-count').text(remainingCards);
        $('#discard-count').text(discardedCards);
        const deckProgress = (remainingCards / totalCards) * 100;
        const discardProgress = (discardedCards / totalCards) * 100;
        $('#deck-progress-fill').css('width', `${deckProgress}%`);
        $('#discard-progress-fill').css('width', `${discardProgress}%`);
        const deckCard = $('#main-deck .deck-card');
        if (remainingCards === 0) {
            deckCard.css('opacity', '0.3');
        } else if (remainingCards < totalCards * 0.2) {
            deckCard.css('opacity', '0.6');
        } else {
            deckCard.css('opacity', '1');
        }
    }
    function shuffleDeck() {
        const shufflePasses = 3 + Math.floor(Math.random() * 3);
        for (let pass = 0; pass < shufflePasses; pass++) {
            for (let i = gameState.deck.length - 1; i > 0; i--) {
                let randomValue;
                if (window.crypto && window.crypto.getRandomValues) {
                    const array = new Uint32Array(1);
                    window.crypto.getRandomValues(array);
                    randomValue = array[0] / (0xFFFFFFFF + 1);
                } else {
                    randomValue = Math.random();
                }
                const j = Math.floor(randomValue * (i + 1));
                [gameState.deck[i], gameState.deck[j]] = [gameState.deck[j], gameState.deck[i]];
            }
            if (pass < shufflePasses - 1) {
                riffleShuffle();
            }
        }
        updateDeckDisplay();
    }
    function riffleShuffle() {
        const deckSize = gameState.deck.length;
        const splitPoint = Math.floor(deckSize / 2) + Math.floor(Math.random() * 6) - 3;
        const leftHalf = gameState.deck.slice(0, splitPoint);
        const rightHalf = gameState.deck.slice(splitPoint);
        gameState.deck = [];
        let leftIndex = 0;
        let rightIndex = 0;
        while (leftIndex < leftHalf.length || rightIndex < rightHalf.length) {
            const takeFromLeft = leftIndex < leftHalf.length &&
                (rightIndex >= rightHalf.length || Math.random() < 0.5);
            if (takeFromLeft) {
                gameState.deck.push(leftHalf[leftIndex++]);
            } else {
                gameState.deck.push(rightHalf[rightIndex++]);
            }
        }
    }
    function dealCard() {
        if (gameState.deck.length === 0) {
            triggerShuffleSync();
        }
        const card = gameState.deck.pop();
        if (!card) {
            triggerShuffleSync();
            return gameState.deck.pop();
        }
        updateDeckDisplay();
        return card;
    }
    function triggerShuffleSync() {
        if (gameState.isShuffling) {
            return;
        }
        gameState.isShuffling = true;
        gameState.deck = [...gameState.discardPile];
        gameState.discardPile = [];
        shuffleDeck();
        gameState.isShuffling = false;
    }
    function triggerShuffleWithAnimation() {
        if (gameState.isShuffling) {
            return Promise.resolve();
        }
        gameState.isShuffling = true;
        updateGameStatus(window.i18n ? window.i18n.t('messages.shuffling') : 'Shuffling...');
        $('.deck-card').addClass('shuffling');
        return new Promise(resolve => {
            safeSetTimeout(() => {
                gameState.deck = [...gameState.discardPile];
                gameState.discardPile = [];
                shuffleDeck();
                $('.deck-card').removeClass('shuffling');
                gameState.isShuffling = false;
                updateDeckDisplay();
                resolve();
            }, 2000);
        });
    }
    function calculateScore(cards) {
        if (!cards || !Array.isArray(cards)) {
            return 0;
        }
        let score = 0;
        let aces = 0;
        for (let card of cards) {
            if (!card || !card.value) {
                continue;
            }
            if (card.value === 'A') {
                aces++;
                score += 11;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                score += 10;
            } else {
                score += parseInt(card.value);
            }
        }
        while (score > 21 && aces > 0) {
            score -= 10;
            aces--;
        }
        return score;
    }
    function getScoreDisplay(cards) {
        if (!cards || !Array.isArray(cards)) {
            return '0';
        }
        let hardScore = 0;
        let aces = 0;
        for (let card of cards) {
            if (!card || !card.value) {
                continue;
            }
            if (card.value === 'A') {
                aces++;
                hardScore += 1;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                hardScore += 10;
            } else {
                hardScore += parseInt(card.value);
            }
        }
        if (aces === 0) {
            return hardScore.toString();
        }
        let softScore = hardScore + 10; 
        if (softScore === 21) {
            return '21';
        }
        if (softScore > 21) {
            return hardScore.toString();
        }
        if (hardScore === softScore) {
            return hardScore.toString();
        }
        return `${hardScore}/${softScore}`;
    }
    function canPlayerSplit(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.cards.length !== 2 || player.splitHands.length > 0) {
            return false;
        }
        if (!player.cards[0] || !player.cards[1]) {
            return false;
        }
        const card1Value = player.cards[0].value;
        const card2Value = player.cards[1].value;
        const isSameValue = card1Value === card2Value;
        const hasEnoughBalance = player.isAI || gameState.balance >= player.bet;
        return isSameValue && hasEnoughBalance;
    }
    function splitPlayerHand(playerIndex) {
        const player = gameState.players[playerIndex];
        if (!canPlayerSplit(playerIndex)) {
            return false;
        }
        if (!player.isAI) {
            if (gameState.balance < player.bet) {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalanceForSplit') : 'Insufficient balance for split!');
                return false;
            }
            gameState.balance -= player.bet;
        }
        animateSplitBetChips(player.bet, playerIndex);
        const firstCard = player.cards[0];
        const secondCard = player.cards[1];
        player.splitHands = [
            { cards: [firstCard], score: 0, bet: player.bet, isBust: false, isComplete: false },
            { cards: [secondCard], score: 0, bet: player.bet, isBust: false, isComplete: false }
        ];
        player.cards = [];
        player.currentHandIndex = 0;
        player.splitHands[0].score = calculateScore(player.splitHands[0].cards);
        player.splitHands[1].score = calculateScore(player.splitHands[1].cards);
        playSound('deal');
        updateDisplay();
        safeSetTimeout(() => {
            updateBetDisplay(playerIndex, player.bet * 2);
        }, 200);
        performSplitAnimation(playerIndex, () => {
            displaySplitHands(playerIndex, -1);
            safeSetTimeout(() => {
                player.splitHands[0].cards.push(dealCard());
                player.splitHands[0].score = calculateScore(player.splitHands[0].cards);
                playSound('deal');
                displaySplitHands(playerIndex, 1);
            }, 300);
        });
        const totalBetAfterSplit = player.bet * 2;
        const message = window.i18n ?
            window.i18n.t('messages.playerChoseToSplit', { amount: totalBetAfterSplit.toLocaleString() }) :
            `Player chose to split! Total bet: ${totalBetAfterSplit.toLocaleString()} - Hand 1 - Hit or Stand?`;
        updateGameStatus(message);
        return true;
    }
    function moveToNextSplitHand(playerIndex, callback) {
        const player = gameState.players[playerIndex];
        if (player.splitHands.length === 0) {
            if (callback) callback(false);
            return false;
        }
        player.splitHands[player.currentHandIndex].isComplete = true;
        const currentHand = player.splitHands[player.currentHandIndex];
        const delay = currentHand.isBust ? 1000 : 0;
        safeSetTimeout(() => {
            player.currentHandIndex++;
            if (player.currentHandIndex < player.splitHands.length) {
                performSplitHandTransition(playerIndex, () => {
                    displaySplitHands(playerIndex, -1);
                    const nextHand = player.splitHands[player.currentHandIndex];
                    let newCardIndex = -1;
                    if (nextHand.cards.length === 1) {
                        nextHand.cards.push(dealCard());
                        nextHand.score = calculateScore(nextHand.cards);
                        newCardIndex = 1;
                        playSound('deal');
                        displaySplitHands(playerIndex, newCardIndex);
                    }
                    const message = window.i18n ?
                        window.i18n.t('messages.handHitOrStand', { handNumber: player.currentHandIndex + 1 }) :
                        `Hand ${player.currentHandIndex + 1} - Hit or Stand?`;
                    updateGameStatus(message);
                    updateSplitButtonVisibility();
                    if (callback) callback(true);
                });
                return true;
            } else {
                displaySplitHandsFinal(playerIndex);
                if (callback) callback(false);
                return false;
            }
        }, delay);
        return true;
    }
    function performSplitAnimation(playerIndex, callback) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const cards = cardsContainer.find('.card');
        if (cards.length >= 2) {
            const secondCard = cards.eq(1);
            cards.removeClass('dealt').css('animation', 'none');
            const animatedCard = secondCard.clone();
            animatedCard.removeClass('dealt');
            animatedCard.addClass('splitting-card');
            animatedCard.css({
                position: 'absolute',
                top: secondCard.position().top,
                left: secondCard.position().left,
                zIndex: 100,
                animation: 'none'
            });
            cardsContainer.append(animatedCard);
            secondCard.css('opacity', '0');
            const tempMiniHand = $('<div class="mini-hand" style="visibility: hidden;"></div>');
            cardsContainer.append(tempMiniHand);
            const miniPosition = tempMiniHand.position();
            const miniTargetLeft = miniPosition.left;
            const miniTargetTop = miniPosition.top;
            tempMiniHand.remove();
            animatedCard.animate({
                left: miniTargetLeft + 'px',
                top: miniTargetTop + 'px'
            }, {
                duration: 300, 
                easing: 'swing',
                step: function(now, fx) {
                    if (fx.prop === 'left') {
                        const progress = Math.abs(now - fx.start) / Math.abs(fx.end - fx.start);
                        const scale = 1 - (progress * 0.5);
                        $(this).css({
                            'transform': `scale(${scale})`,
                            'transform-origin': 'top left'
                        });
                    }
                },
                complete: function() {
                    $(this).css({
                        'transform': 'scale(0.5)',
                        'transform-origin': 'top left',
                        'left': miniTargetLeft + 'px',
                        'top': miniTargetTop + 'px'
                    });
                    safeSetTimeout(() => {
                        animatedCard.remove();
                        callback();
                    }, 50);
                }
            });
        } else {
            safeSetTimeout(callback, 100);
        }
    }
    function performSplitHandTransition(playerIndex, callback) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const splitContainer = cardsContainer.find('.split-hands-container');
        if (splitContainer.length === 0) {
            safeSetTimeout(callback, 500);
            return;
        }
        splitContainer.addClass('transitioning');
        safeSetTimeout(() => {
            splitContainer.removeClass('transitioning');
            callback();
        }, 500);
    }
    function displaySplitHands(playerIndex, newCardIndex = -1) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        let splitContainer = cardsContainer.find('.split-hands-container');
        if (splitContainer.length === 0) {
            cardsContainer.empty();
            splitContainer = $('<div class="split-hands-container"></div>');
            cardsContainer.append(splitContainer);
        }
        if (newCardIndex === -1) {
            splitContainer.find('.current-hand, .mini-hand').remove();
        }
        player.splitHands.forEach((hand, handIndex) => {
            if (handIndex === player.currentHandIndex) {
                let currentHandContainer = splitContainer.find('.current-hand');
                if (currentHandContainer.length === 0) {
                    currentHandContainer = $('<div class="current-hand"></div>');
                    splitContainer.append(currentHandContainer);
                }
                if (newCardIndex >= 0 && newCardIndex < hand.cards.length) {
                    const newCard = hand.cards[newCardIndex];
                    if (newCard) {
                        const cardElement = createCardElement(newCard);
                        cardElement.addClass('dealt');
                        currentHandContainer.append(cardElement);
                    }
                } else {
                    currentHandContainer.find('.card').remove(); 
                    hand.cards.forEach((card, cardIndex) => {
                        if (card) {
                            const cardElement = createCardElement(card);
                            cardElement.removeClass('dealt');
                            cardElement.css('animation', 'none');
                            currentHandContainer.append(cardElement);
                        }
                    });
                }
                let currentScore = currentHandContainer.find('.split-hand-score.current-score');
                if (currentScore.length === 0) {
                } else {
                    currentScore.text(getScoreDisplay(hand.cards));
                }
            } else {
                let miniHandContainer = splitContainer.find(`[data-hand="${handIndex}"]`);
                if (miniHandContainer.length === 0) {
                    miniHandContainer = $(`<div class="mini-hand" data-hand="${handIndex}"></div>`);
                    miniHandContainer.css({
                        'position': 'absolute',
                        'top': '-10px',
                        'left': '-80px',
                        'transform': 'scale(0.5)',
                        'transform-origin': 'top left'
                    });
                    splitContainer.append(miniHandContainer);
                    hand.cards.forEach(card => {
                        if (card) {
                            const miniCard = createCardElement(card);
                            miniCard.addClass('mini-card');
                            miniCard.removeClass('dealt');
                            miniCard.css('animation', 'none');
                            miniHandContainer.append(miniCard);
                        }
                    });
                } else {
                    miniHandContainer.find('.card').remove();
                    hand.cards.forEach(card => {
                        if (card) {
                            const miniCard = createCardElement(card);
                            miniCard.addClass('mini-card');
                            miniCard.removeClass('dealt');
                            miniCard.css('animation', 'none');
                            miniHandContainer.append(miniCard);
                        }
                    });
                    miniHandContainer.find('.mini-score').text(getScoreDisplay(hand.cards));
                }
                miniHandContainer.removeClass('completed bust won lost push blackjack twenty-one');
                if (hand.isComplete) {
                    miniHandContainer.addClass('completed');
                    if (hand.isBust) {
                        miniHandContainer.addClass('bust');
                    }
                    if (hand.effectClass) {
                        miniHandContainer.addClass(hand.effectClass);
                    }
                }
            }
        });
        const currentHand = player.splitHands[player.currentHandIndex];
        const htmlPos = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPos}`).text(`Hand ${player.currentHandIndex + 1}: ${getScoreDisplay(currentHand.cards)}`);
    }
    function displaySplitHandsFinal(playerIndex) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        cardsContainer.empty();
        const finalContainer = $('<div class="split-hands-final-container"></div>');
        cardsContainer.append(finalContainer);
        player.splitHands.forEach((hand, handIndex) => {
            const handContainer = $(`<div class="final-hand" data-hand="${handIndex}"></div>`);
            finalContainer.append(handContainer);
            const cardsArea = $('<div class="player-cards-area"></div>');
            handContainer.append(cardsArea);
            hand.cards.forEach(card => {
                const cardElement = createCardElement(card);
                cardsArea.append(cardElement);
            });
            if (hand.isBust) {
                handContainer.addClass('bust');
            }
            if (hand.effectClass) {
                handContainer.addClass(hand.effectClass);
            }
        });
        const hand1Score = getScoreDisplay(player.splitHands[0].cards);
        const hand2Score = getScoreDisplay(player.splitHands[1].cards);
        const htmlPos = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPos}`).text(`Hand 1: ${hand1Score} | Hand 2: ${hand2Score}`);
    }
    function applySplitHandsSettlement(playerIndex, handResults) {
        const player = gameState.players[playerIndex];
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const cardsContainer = $(`#player-cards-${htmlPosition}`);
        const handContainers = cardsContainer.find('.final-hand');
        player.splitHands.forEach((hand, handIndex) => {
            const handContainer = handContainers.eq(handIndex);
            if (handContainer.length > 0 && handResults && handResults[handIndex]) {
                handContainer.removeClass('bust won lost push blackjack twenty-one');
                delete hand.effectClass;
                handContainer.addClass(handResults[handIndex]);
            } else {
                console.warn(`Could not find container for hand ${handIndex}`);
            }
        });
    }
    function checkPerfectPairs(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.cards.length < 2 || player.sideBets.perfectPairs === 0) {
            return 0;
        }
        const card1 = player.cards[0];
        const card2 = player.cards[1];
        if (card1.value === card2.value && card1.suit === card2.suit) {
            return player.sideBets.perfectPairs * 25;
        }
        if (card1.value === card2.value && card1.color === card2.color) {
            return player.sideBets.perfectPairs * 12;
        }
        if (card1.value === card2.value) {
            return player.sideBets.perfectPairs * 6;
        }
        return 0;
    }
    function checkTwentyOnePlusThree(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.cards.length < 2 || gameState.dealerCards.length < 1 || player.sideBets.twentyOnePlusThree === 0) {
            return 0;
        }
        const playerCard1 = player.cards[0];
        const playerCard2 = player.cards[1];
        const dealerCard = gameState.dealerCards[0];
        const cards = [playerCard1, playerCard2, dealerCard];
        if (cards.every(card => card.value === cards[0].value && card.suit === cards[0].suit)) {
            return player.sideBets.twentyOnePlusThree * 100;
        }
        if (isStraightFlush(cards)) {
            return player.sideBets.twentyOnePlusThree * 40;
        }
        if (isThreeOfAKind(cards)) {
            return player.sideBets.twentyOnePlusThree * 30;
        }
        if (isStraight(cards)) {
            return player.sideBets.twentyOnePlusThree * 10;
        }
        if (isFlush(cards)) {
            return player.sideBets.twentyOnePlusThree * 5;
        }
        return 0;
    }
    function isStraightFlush(cards) {
        return isStraight(cards) && isFlush(cards);
    }
    function isThreeOfAKind(cards) {
        const values = cards.map(card => card.value);
        return values.every(value => value === values[0]);
    }
    function isStraight(cards) {
        const values = cards.map(card => {
            if (card.value === 'A') return 1;
            if (card.value === 'J') return 11;
            if (card.value === 'Q') return 12;
            if (card.value === 'K') return 13;
            return parseInt(card.value);
        }).sort((a, b) => a - b);
        for (let i = 1; i < values.length; i++) {
            if (values[i] !== values[i-1] + 1) {
                return false;
            }
        }
        return true;
    }
    function isFlush(cards) {
        const suits = cards.map(card => card.suit);
        return suits.every(suit => suit === suits[0]);
    }
    function updateSplitButtonVisibility() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const splitButton = $('#split');
        if (gameState.currentTurnIndex === gameState.currentPlayerIndex &&
            currentPlayer.splitHands.length === 0 &&
            currentPlayer.cards.length === 2 &&
            canPlayerSplit(gameState.currentPlayerIndex)) {
            splitButton.show().prop('disabled', false);
        } else {
            splitButton.hide();
        }
    }
    function forceSyncSideBetDisplays() {
        gameState.players.forEach((player, index) => {
            const htmlPosition = getPlayerHtmlPosition(index);
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot`).removeClass('has-bet');
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-chips-stack`).remove();
            Object.keys(player.sideBets).forEach(betType => {
                const betAmount = player.sideBets[betType] || 0;
                updateSideBetChipDisplay(index, betType, betAmount);
                const sideBetSpot = $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot[data-type="${betType}"]`);
                if (betAmount > 0) {
                    sideBetSpot.addClass('has-bet');
                } else {
                    sideBetSpot.removeClass('has-bet');
                }
            });
        });
    }
    function updatePlayerSideBetDisplay(playerIndex) {
        const player = gameState.players[playerIndex];
        updateSideBetChipDisplay(playerIndex, 'perfectPairs', player.sideBets.perfectPairs);
        updateSideBetChipDisplay(playerIndex, 'twentyOnePlusThree', player.sideBets.twentyOnePlusThree);
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot`).each(function() {
            const betType = $(this).data('type');
            const betAmount = player.sideBets[betType] || 0;
            if (betAmount > 0) {
                $(this).addClass('has-bet');
            } else {
                $(this).removeClass('has-bet');
            }
        });
    }
    function updateSideBetChipDisplay(playerIndex, betType, totalBet) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const sideBetSpot = $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot[data-type="${betType}"]`);
        const betAmountElement = sideBetSpot.find('.side-bet-amount');
        sideBetSpot.find('.side-bet-chips-stack').remove();
        if (totalBet === 0) {
            betAmountElement.text('0');
            return;
        }
        const formattedAmount = formatBetAmount(totalBet);
        betAmountElement.text(formattedAmount);
        const chipStack = $('<div class="side-bet-chips-stack"></div>');
        const chips = calculateChipBreakdown(totalBet);
        const currentDenominations = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get().sort((a, b) => a - b);
        const denominations = currentDenominations.length > 0 ? currentDenominations : generateChipDenominations(gameState.balance);
        chips.forEach((chip) => {
            const value = chip.value * 1000;
            const chipClass = getChipPreviewClass(value, denominations);
            const chipElement = $(`<div class="side-bet-chip-preview ${chipClass}"></div>`);
            // 大金额筹码在下面，小金额筹码在上面
            chipStack.append(chipElement);
        });
        sideBetSpot.append(chipStack);
    }
    function showSideBetControls() {
        $('#side-bet-controls').fadeIn();
    }
    function hideSideBetControls() {
        $('#side-bet-controls').fadeOut();
    }
    function processSideBetWinnings(playerIndex) {
        const player = gameState.players[playerIndex];
        let totalWinnings = 0;
        const perfectPairsWin = checkPerfectPairs(playerIndex);
        if (perfectPairsWin > 0) {
            totalWinnings += perfectPairsWin;
            showActionBubble(playerIndex, `Perfect Pairs won ${perfectPairsWin.toLocaleString()}`, 'hit');
        }
        const twentyOnePlusThreeWin = checkTwentyOnePlusThree(playerIndex);
        if (twentyOnePlusThreeWin > 0) {
            totalWinnings += twentyOnePlusThreeWin;
            safeSetTimeout(() => {
                showActionBubble(playerIndex, `21+3 won ${twentyOnePlusThreeWin.toLocaleString()}`, 'hit');
            }, 1000);
        }
        if (totalWinnings > 0) {
            if (playerIndex === gameState.currentPlayerIndex) {
                gameState.balance += totalWinnings;
                updateDisplay();
            }
        }
        return totalWinnings;
    }
    function createCardElement(card, isHidden = false) {
        if (isHidden) {
            return $('<div class="card card-back"></div>');
        }
        if (!card) {
            console.error('Cannot create card element: card is undefined');
            return $('<div class="card error-card">ERROR</div>');
        }
        if (!card.value || !card.suit || !card.color) {
            console.error('Cannot create card element: card properties are missing', card);
            return $('<div class="card error-card">ERROR</div>');
        }
        const cardElement = $(`
            <div class="card ${card.color}">
                <div class="card-corner top-left">
                    <div class="card-value">${card.value}</div>
                    <div class="card-suit">${card.suit}</div>
                </div>
                <div class="card-center">
                    <div class="card-suit-large">${card.suit}</div>
                </div>
                <div class="card-corner bottom-right">
                    <div class="card-value">${card.value}</div>
                    <div class="card-suit">${card.suit}</div>
                </div>
            </div>
        `);
        safeSetTimeout(() => {
            cardElement.addClass('dealt');
        }, 100);
        return cardElement;
    }
    let backgroundMusic = null;
    let dealSound = null;
    let winSound = null;
    let isMuted = false;
    let musicMuted = false;
    let effectsMuted = false;
    let musicVolume = 0.2;
    let effectsVolume = 0.8;
    let audioInitialized = false;
    let audioLoadPromises = [];

    window.addEventListener('languageChanged', function() {
        updateGameTexts();
    });

    function updateGameTexts() {
        if (window.i18n) {
            const currentStatus = $('#game-status').text();

            if (currentStatus.includes('place your bet') || currentStatus.includes('Setzen Sie')) {
                updateGameStatus(window.i18n.t('ui.gameStatus'));
            } else if (currentStatus.includes('Hit or Stand') || currentStatus.includes('Karte oder Halten')) {
                updateGameStatus(window.i18n.t('messages.yourTurn'));
            } else if (currentStatus.includes('Click Ready') || currentStatus.includes('Klicken Sie')) {
                updateGameStatus(window.i18n.t('messages.clickDoneToStart'));
            } else if (currentStatus.includes('Welcome') || currentStatus.includes('Willkommen')) {
                updateGameStatus(window.i18n.t('messages.welcomePlaceBet'));
            } else if (currentStatus.includes('Shuffling') || currentStatus.includes('gemischt')) {
                updateGameStatus(window.i18n.t('messages.shuffling'));
            } else if (currentStatus.includes('Calculating') || currentStatus.includes('berechnet')) {
                updateGameStatus(window.i18n.t('messages.calculatingResults'));
            }
        }
    }
    function initAudio() {
        if (audioInitialized) {
            return Promise.resolve();
        }

        try {
            // 清理旧的音频对象
            cleanupAudio();

            // 创建新的音频对象
            backgroundMusic = new Audio('/blackjack-simulator/audio/backJazz.mp3');
            backgroundMusic.loop = true;
            backgroundMusic.volume = musicVolume;
            backgroundMusic.preload = 'metadata';

            dealSound = new Audio('/blackjack-simulator/audio/deal.mp3');
            dealSound.volume = effectsVolume;
            dealSound.preload = 'metadata';

            winSound = new Audio('/blackjack-simulator/audio/win.mp3');
            winSound.volume = effectsVolume;
            winSound.preload = 'metadata';

            // 添加错误处理
            [backgroundMusic, dealSound, winSound].forEach(audio => {
                audio.addEventListener('error', (e) => {
                    console.warn('Audio load error:', e);
                });

                audio.addEventListener('loadeddata', () => {
                    console.log('Audio loaded successfully');
                });
            });

            loadAudioSettings();
            audioInitialized = true;

        } catch (error) {
            console.warn('Audio initialization failed:', error);
        }
    }

    function cleanupAudio() {
        if (backgroundMusic) {
            backgroundMusic.pause();
            backgroundMusic.src = '';
            backgroundMusic = null;
        }
        if (dealSound) {
            dealSound.pause();
            dealSound.src = '';
            dealSound = null;
        }
        if (winSound) {
            winSound.pause();
            winSound.src = '';
            winSound = null;
        }
        audioInitialized = false;
    }
    function playBackgroundMusic() {
        if (backgroundMusic && !musicMuted && !isMuted) {
            backgroundMusic.volume = musicVolume;
            backgroundMusic.play().catch(error => {
            });
        }
    }
    function toggleFullscreen() {
        const isFullscreen = document.fullscreenElement ||
                           document.webkitFullscreenElement ||
                           document.mozFullScreenElement ||
                           document.msFullscreenElement;

        if (!isFullscreen) {
            const docEl = document.documentElement;
            const requestFullscreen = docEl.requestFullscreen ||
                                    docEl.webkitRequestFullscreen ||
                                    docEl.mozRequestFullScreen ||
                                    docEl.msRequestFullscreen;

            if (requestFullscreen) {
                requestFullscreen.call(docEl).then(() => {
                    updateFullscreenButton(true);
                    if (typeof setNonGameSectionsVisibility === 'function') {
                        setNonGameSectionsVisibility(false);
                    }
                    setTimeout(lockScreenOrientation, 200);
                }).catch(err => {
                    updateFullscreenButton(false);
                });
            }
        } else {
            const exitFullscreen = document.exitFullscreen ||
                                  document.webkitExitFullscreen ||
                                  document.mozCancelFullScreen ||
                                  document.msExitFullscreen;

            if (exitFullscreen) {
                exitFullscreen.call(document).then(() => {
                    updateFullscreenButton(false);
                    if (typeof setNonGameSectionsVisibility === 'function') {
                        setNonGameSectionsVisibility(true);
                    }
                }).catch(err => {
                    updateFullscreenButton(false);
                });
            }
        }
    }
    function setNonGameSectionsVisibility(shouldShow) {
        try {
            const display = shouldShow ? '' : 'none';
            document.querySelectorAll('.seo-content-section').forEach(el => { el.style.display = display; });
            document.querySelectorAll('.game-recommendations.similar-games').forEach(el => { el.style.display = display; });
            document.querySelectorAll('.game-recommendations.other-games').forEach(el => { el.style.display = display; });
        } catch (_) {}
    }
    function updateFullscreenButton(isFullscreen) {
        const fullscreenIcon = $('#fullscreen-icon');
        if (isFullscreen) {
            fullscreenIcon.text('⛶');
            $('#fullscreen-button').attr('title', 'Exit Fullscreen');
        } else {
            fullscreenIcon.text('⛶');
            $('#fullscreen-button').attr('title', 'Enter Fullscreen');
        }
    }
    function openAudioSettings() {
        if ($('#audio-settings-modal').length === 0) {
            createAudioSettingsModal();
        }
        updateAudioSettingsUI();
        $('#audio-settings-modal').css('display', 'flex');
    }

    function toggleMute() {
        isMuted = !isMuted;
        const muteButton = $('#mute-button');
        const muteIcon = $('.mute-icon');
        if (isMuted) {
            if (backgroundMusic) {
                backgroundMusic.pause();
            }
            muteButton.addClass('muted');
            muteIcon.text('🔇');
        } else {
            if (gameState.gameStarted) {
                playBackgroundMusic();
            }
            muteButton.removeClass('muted');
            muteIcon.text('🔊');
        }
    }
    function playSound(soundType) {
        if (effectsMuted || isMuted) return;

        try {
            switch (soundType) {
                case 'cardDeal':
                case 'cardFlip':
                case 'hit':
                    if (dealSound) {
                        dealSound.volume = effectsVolume;
                        dealSound.currentTime = 0;
                        dealSound.play().catch(error => {
                        });
                    }
                    break;
                case 'win':
                    if (winSound) {
                        if (backgroundMusic && !musicMuted && !isMuted) {
                            backgroundMusic.pause();
                        }
                        winSound.volume = effectsVolume;
                        winSound.currentTime = 0;
                        winSound.play().catch(error => {
                        });
                    }
                    break;
                case 'chipPlace':
                    break;
                case 'lose':
                    break;
                default:
                    break;
            }
        } catch (error) {
        }
    }
    function safeDeductBalance(amount) {
        if (gameState.balance >= amount && amount > 0) {
            gameState.balance -= amount;
            return true;
        }
        return false;
    }
    function ensureBalanceIntegrity() {
        if (gameState.balance < 0) {
            console.warn('Balance became negative, resetting to 0');
            gameState.balance = 0;
        }
        checkGameOverCondition();
    }
    function checkGameOverCondition() {
        if (gameState.balance === 0 && !gameState.gameInProgress && !gameState.gameOverModalShown) {
            const hasAnyBets = gameState.players.some(player => player.bet > 0);
            if (!hasAnyBets) {
                showGameOverModal();
            }
        }
    }
    function showGameOverModal() {
        if (gameState.gameOverModalShown) {
            return; // Prevent duplicate modals
        }
        gameState.gameOverModalShown = true;
        // Show share incentive modal instead of game over modal
        showShareIncentiveModal();
        if (backgroundMusic && !backgroundMusic.paused) {
            backgroundMusic.pause();
        }
        playSound('lose');
    }

    function showShareIncentiveModal() {
        $('#share-incentive-modal').addClass('show').css('display', 'flex').hide().fadeIn(300);
        bindShareIncentiveControls();
    }

    function hideShareIncentiveModal() {
        $('#share-incentive-modal').fadeOut(300, function() {
            $(this).removeClass('show');
        });
    }

    function bindShareIncentiveControls() {
        // Unbind previous events to prevent duplicates
        $('#share-facebook-incentive, #share-twitter-incentive, #share-whatsapp-incentive, #share-telegram-incentive, #skip-share-btn').off('click');

        const currentUrl = window.location.href;
        const gameTitle = document.title || 'Blackjack Simulator';

        // Random share texts for variety
        const shareTexts = [
            `Master the art of Blackjack! 🎰 Play ${gameTitle} for free and become a card counting pro! ♠️♥️`,
            `🃏 Ready to beat the dealer? Try ${gameTitle} - the most realistic blackjack experience online! 🎯`,
            `💰 Test your blackjack strategy with ${gameTitle}! Free to play, endless fun! 🎲♠️`,
            `🎰 Think you can count cards? Challenge yourself with ${gameTitle} - no download needed! 🃏`,
            `♠️ Experience Vegas-style blackjack from home! Play ${gameTitle} and perfect your skills! 🎯`,
            `🎲 From beginner to pro - ${gameTitle} has everything you need to master blackjack! 💎`,
            `🃏 Split, double down, and win big! ${gameTitle} offers the ultimate blackjack challenge! 🏆`,
            `💰 Free blackjack simulator that feels like the real casino! Try ${gameTitle} now! 🎰`,
            `🎯 Practice makes perfect! Sharpen your blackjack skills with ${gameTitle} - completely free! ♠️`,
            `🏆 Ready for 21? ${gameTitle} brings authentic casino blackjack to your browser! 🎲♥️`
        ];

        const shareText = shareTexts[Math.floor(Math.random() * shareTexts.length)];

        // Facebook share
        $('#share-facebook-incentive').on('click', function() {
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`;
            const shareWindow = window.open(facebookUrl, '_blank', 'width=600,height=400');
            setupShareReturnMonitoring(shareWindow);
        });

        // Twitter share
        $('#share-twitter-incentive').on('click', function() {
            const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
            const shareWindow = window.open(twitterUrl, '_blank', 'width=600,height=400');
            setupShareReturnMonitoring(shareWindow);
        });

        // WhatsApp share
        $('#share-whatsapp-incentive').on('click', function() {
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + currentUrl)}`;
            const shareWindow = window.open(whatsappUrl, '_blank');
            setupShareReturnMonitoring(shareWindow);
        });

        // Telegram share
        $('#share-telegram-incentive').on('click', function() {
            const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
            const shareWindow = window.open(telegramUrl, '_blank');
            setupShareReturnMonitoring(shareWindow);
        });

        // Skip share button
        $('#skip-share-btn').on('click', function() {
            hideShareIncentiveModal();
            // Give 500 chips instead of full restart
            grantSkipBonus();
        });
    }

    function setupShareReturnMonitoring(shareWindow) {
        let shareCompleted = false;
        let shareStartTime = Date.now();

        // Monitor window close and page visibility
        const checkShareCompletion = () => {
            if (shareCompleted) return;

            const timeSpent = Date.now() - shareStartTime;
            // Consider share successful if user spent at least 3 seconds on share page
            if (timeSpent >= 3000) {
                shareCompleted = true;
                grantShareBonus();
            } else {
                // Give partial bonus for attempt
                grantSkipBonus();
            }
        };

        // Check if share window is closed
        const windowChecker = setInterval(() => {
            if (shareWindow.closed) {
                clearInterval(windowChecker);
                checkShareCompletion();
            }
        }, 1000);

        // Listen for page visibility change (user returns to game)
        const visibilityHandler = () => {
            if (!document.hidden && shareWindow.closed) {
                document.removeEventListener('visibilitychange', visibilityHandler);
                checkShareCompletion();
            }
        };
        document.addEventListener('visibilitychange', visibilityHandler);

        // Listen for window focus (user returns to game)
        const focusHandler = () => {
            if (shareWindow.closed) {
                window.removeEventListener('focus', focusHandler);
                checkShareCompletion();
            }
        };
        window.addEventListener('focus', focusHandler);

        // Cleanup after 30 seconds
        setTimeout(() => {
            clearInterval(windowChecker);
            document.removeEventListener('visibilitychange', visibilityHandler);
            window.removeEventListener('focus', focusHandler);
            if (!shareCompleted && !shareWindow.closed) {
                shareWindow.close();
                checkShareCompletion();
            }
        }, 30000);
    }

    function grantShareBonus() {
        // Grant 5000 bonus chips
        gameState.balance = 5000;
        gameState.balanceCache = 5000;

        // Cache the balance properly
        cacheUserBalance();

        // Reset game over state and game states
        gameState.gameOverModalShown = false;
        gameState.gameInProgress = false;
        gameState.gamePhase = 'betting';
        gameState.gameStarted = true;

        // Reset player states
        gameState.players.forEach(player => {
            player.bet = 0;
            player.cards = [];
            player.score = 0;
            player.isActive = false;
            player.splitHands = [];
        });

        // Clear game display
        $('.dealer-cards-area').empty();
        $('.player-cards-area').empty();
        $('.bet-amount').text('0');
        $('.bet-chips-stack').empty();
        $('.player-position, .dealer-section').removeClass('won lost bust push blackjack twenty-one');

        // Show betting controls
        $('#betting-controls').show();
        $('#action-controls').hide();

        // Hide modal and update display
        hideShareIncentiveModal();
        updateDisplay();
        updateChipDenominations(); // 重新生成筹码按钮
        updateChipButtonStates();

        // Show game status
        updateGameStatus('You received 5000 bonus chips! Place your bets to start playing.');

        // Show success message
        showBonusMessage();
    }

    function grantSkipBonus() {
        // Grant 500 bonus chips for skipping share
        gameState.balance = 500;
        gameState.balanceCache = 500;

        // Cache the balance properly
        cacheUserBalance();

        // Reset game over state and game states
        gameState.gameOverModalShown = false;
        gameState.gameInProgress = false;
        gameState.gamePhase = 'betting';
        gameState.gameStarted = true;

        // Reset player states
        gameState.players.forEach(player => {
            player.bet = 0;
            player.cards = [];
            player.score = 0;
            player.isActive = false;
            player.splitHands = [];
        });

        // Clear game display
        $('.dealer-cards-area').empty();
        $('.player-cards-area').empty();
        $('.bet-amount').text('0');
        $('.bet-chips-stack').empty();
        $('.player-position, .dealer-section').removeClass('won lost bust push blackjack twenty-one');

        // Show betting controls
        $('#betting-controls').show();
        $('#action-controls').hide();

        // Update display
        updateDisplay();
        updateChipDenominations(); // 重新生成筹码按钮
        updateChipButtonStates();

        // Show game status
        updateGameStatus('You received 500 chips! Place your bets to start playing.');
    }

    function showBonusMessage() {
        // Create and show bonus message
        const bonusMessage = $('<div class="bonus-message">🎁 You received 5000 bonus chips! Thanks for sharing!</div>');
        bonusMessage.css({
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'linear-gradient(145deg, #28a745, #20c997)',
            color: 'white',
            padding: '20px 30px',
            borderRadius: '15px',
            fontSize: '18px',
            fontWeight: 'bold',
            textAlign: 'center',
            zIndex: 10000,
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
            opacity: 0
        });

        $('body').append(bonusMessage);

        // Animate in
        bonusMessage.animate({ opacity: 1 }, 300);

        // Remove after 3 seconds
        setTimeout(() => {
            bonusMessage.animate({ opacity: 0 }, 300, function() {
                bonusMessage.remove();
            });
        }, 3000);
    }
    function hideGameOverModal() {
        $('#game-over-modal').fadeOut(300, function() {
            $(this).removeClass('show');
        });
    }

    // Insurance functions
    function offerInsurance() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];

        // Check if current player has blackjack
        const hasBlackjack = currentPlayer.score === 21 && currentPlayer.cards.length === 2;

        // Only offer insurance to human player who doesn't have blackjack
        if (!currentPlayer.isAI && !hasBlackjack) {
            const insuranceCost = Math.floor(currentPlayer.bet / 2);

            if (gameState.balance >= insuranceCost) {
                gameState.insuranceOffered = true;
                $('#insurance-cost').text(insuranceCost);
                $('#insurance-panel').fadeIn(300);
                return; // Wait for player decision
            }
        }

        // Skip insurance for AI players, players with blackjack, or if can't afford
        checkDealerHoleCard();
    }

    function buyInsurance() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const insuranceCost = Math.floor(currentPlayer.bet / 2);

        if (deductBalance(insuranceCost, 'insurance')) {
            currentPlayer.hasInsurance = true;
            currentPlayer.insuranceBet = insuranceCost;
            updateDisplay();
        }

        $('#insurance-panel').fadeOut(300);
        setTimeout(() => {
            checkDealerHoleCard();
        }, 500);
    }

    function declineInsurance() {
        $('#insurance-panel').fadeOut(300);
        setTimeout(() => {
            checkDealerHoleCard();
        }, 500);
    }

    function checkDealerHoleCard() {
        // Check if dealer has blackjack
        if (gameState.dealerScore === 21) {
            // Dealer has blackjack
            gameState.players.forEach(player => {
                if (player.hasInsurance) {
                    // Insurance pays 2:1
                    const insuranceWin = player.insuranceBet * 3; // Original bet + 2:1 payout
                    gameState.balance += insuranceWin;
                    showActionBubble(gameState.currentPlayerIndex,
                        window.i18n ? window.i18n.t('results.insuranceWin') : 'Insurance wins!', 'hit');
                }
            });
            // End game immediately - dealer blackjack
            showGameResults();
        } else {
            // Dealer doesn't have blackjack, continue game
            gameState.players.forEach(player => {
                if (player.hasInsurance) {
                    showActionBubble(gameState.currentPlayerIndex,
                        window.i18n ? window.i18n.t('results.insuranceLose') : 'Insurance loses!', 'bust');
                }
            });
            startPlayerTurns();
        }
    }
    function restartGame() {
        resetBalanceCache();
        gameState.achievements.highestWin = 0;
        gameState.achievements.currentWinStreak = 0;
        gameState.achievements.bestWinStreak = 0;
        gameState.achievements.totalGames = 0;
        gameState.achievements.totalWins = 0;
        gameState.players.forEach(player => {
            player.bet = 0;
            player.cards = [];
            player.score = 0;
            player.sideBets = { perfectPairs: 0, twentyOnePlusThree: 0 };
            player.splitHands = [];
            player.currentSplitIndex = 0;
        });
        gameState.gameInProgress = false;
        gameState.gamePhase = 'waiting';
        gameState.currentTurnIndex = -1;
        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;
        gameState.playerReady = false;
        gameState.autoStartScheduled = false;
        gameState.gameOverModalShown = false;
        if (gameState.countdownTimer) {
            safeClearInterval(gameState.countdownTimer);
            gameState.countdownTimer = null;
        }
        gameState.countdownActive = false;

        // Clear any pending game over checks to prevent duplicate modals
        activeTimers.timeouts.forEach(timeoutId => {
            clearTimeout(timeoutId);
        });
        activeTimers.timeouts.clear();

        hideGameOverModal();
        updateDisplay();
        updateAchievementDisplay();
        updateChipDenominations();
        $('.dealer-cards-area').empty();
        $('.player-cards-area').empty();
        hideAllPlayerScores();
        $('.bet-circle').removeClass('has-bet');
        $('.bet-amount').text('');
        $('.bet-chips-stack').empty();
        $('.side-bet-spot').removeClass('has-bet');
        $('.side-bet-amount').text('');
        $('.side-bet-chips-stack').empty();
        $('.player-position, .dealer-section').removeClass('won lost bust push blackjack twenty-one');
        $('#betting-controls').show();
        $('#action-controls').hide();
        updateGameStatus(window.i18n ? window.i18n.t('messages.welcomeBack') : 'Welcome back! Place your bets to start playing (Min: 5).');
        startBettingCountdown();
        if (backgroundMusic && !musicMuted && !isMuted) {
            setTimeout(() => {
                backgroundMusic.volume = musicVolume;
                backgroundMusic.play().catch(error => {
                });
            }, 500);
        }
    }
    function updateDisplay() {
        ensureBalanceIntegrity();

        // 批量更新余额显示
        const balanceText = gameState.balance.toLocaleString();
        if (domCache.$playerBalanceElements && domCache.$playerBalanceElements.length > 0) {
            domCache.$playerBalanceElements.text(balanceText);
        } else {
            // 回退到直接查询
            $('[id^="player-balance"]').text(balanceText);
        }

        // 更新庄家分数
        const dealerScoreElement = $('#dealer-score');
        if (gameState.dealerHiddenCard && gameState.dealerCards.length > 1) {
            dealerScoreElement.text(getScoreDisplay([gameState.dealerCards[0]]));
        } else {
            dealerScoreElement.text(getScoreDisplay(gameState.dealerCards));
        }

        updateChipDenominationsIfNeeded();
        updateChipButtonStates(); // 确保筹码状态在余额变化后更新

        // 批量更新玩家信息
        const playerUpdates = [];
        gameState.players.forEach((player, index) => {
            const htmlPosition = getPlayerHtmlPosition(index);
            let displayBet = player.bet;
            if (player.splitHands.length > 0) {
                // Calculate total bet for all split hands
                displayBet = player.splitHands.reduce((total, hand) => total + hand.bet, 0);
            }

            playerUpdates.push({
                index,
                htmlPosition,
                displayBet,
                player,
                hasCards: player.splitHands.length === 0
            });
        });

        // 执行批量DOM更新
        playerUpdates.forEach(update => {
            updateBetDisplay(update.index, update.displayBet);

            if (update.hasCards) {
                $(`#player-score-${update.htmlPosition}`).text(getScoreDisplay(update.player.cards));
            }

            const betCircle = $(`.bet-circle[data-position="${update.htmlPosition}"]`);
            betCircle.toggleClass('has-bet', update.player.bet > 0);

            const playerPosition = $(`.player-position[data-position="${update.htmlPosition}"]`);
            playerPosition.toggleClass('active', update.player.isActive);
        });

        // 更新边注显示
        if (gameState.gamePhase !== 'dealing') {
            gameState.players.forEach((player, index) => {
                updatePlayerSideBetDisplay(index);
            });

            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            $('#perfect-pairs-bet').text(formatBetAmount(currentPlayer.sideBets.perfectPairs));
            $('#twenty-one-plus-three-bet').text(formatBetAmount(currentPlayer.sideBets.twentyOnePlusThree));

            // 批量更新边注控制
            $('#side-bet-controls .side-bet-spot').each(function() {
                const $this = $(this);
                const betType = $this.data('type');
                $this.toggleClass('has-bet', currentPlayer.sideBets[betType] > 0);
            });
        }
    }
    function updateBetDisplay(playerIndex, totalBet) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        const betAmountElement = $(`#bet-amount-${htmlPosition}`);
        betCircle.find('.bet-chips-stack').remove();
        if (totalBet === 0) {
            betAmountElement.text('0');
            return;
        }
        const formattedAmount = formatBetAmount(totalBet);
        betAmountElement.text(formattedAmount);
        const chipStack = $('<div class="bet-chips-stack"></div>');
        const chips = calculateChipBreakdown(totalBet);
        const currentDenominations = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get().sort((a, b) => a - b);
        const denominations = currentDenominations.length > 0 ? currentDenominations : generateChipDenominations(gameState.balance);
        chips.forEach((chip) => {
            const value = chip.value * 1000;
            const chipClass = getChipPreviewClass(value, denominations);
            const chipElement = $(`<div class="bet-chip-preview ${chipClass}"></div>`);
            // 大金额筹码在下面，小金额筹码在上面
            chipStack.append(chipElement);
        });
        betCircle.append(chipStack);
    }
    function formatBetAmount(amount) {
        return amount.toLocaleString();
    }
    function calculateChipBreakdown(totalBet) {
        const currentDenominations = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get().sort((a, b) => b - a);
        let chipValues = currentDenominations.length > 0 ? currentDenominations : generateChipDenominations(gameState.balance).sort((a, b) => b - a);
        if (totalBet === 50 || totalBet === 100) {
            const sideBetValues = [100, 50];
            chipValues = [...new Set([...sideBetValues, ...chipValues])].sort((a, b) => b - a);
        }
        const chips = [];
        let remaining = totalBet;

        // 如果下注金额小于5，显示一个5的筹码
        if (totalBet < 5 && totalBet > 0) {
            chips.push({ value: 5 / 1000 });
            return chips;
        }

        for (let value of chipValues) {
            while (remaining >= value && chips.length < 5) {
                chips.push({ value: value / 1000 });
                remaining -= value;
            }
        }
        return chips;
    }
    function generateChipDenominations(balance) {
        const allChipValues = [5, 10, 25, 50, 100, 500, 1000, 5000, 10000];
        return allChipValues;
    }
    let currentChipDenominations = [];

    function updateChipDenominations() {
        const balance = gameState.balance;
        const denominations = generateChipDenominations(balance);

        // 检查是否需要更新
        if (JSON.stringify(denominations) === JSON.stringify(currentChipDenominations)) {
            updateChipButtonStates();
            return;
        }

        currentChipDenominations = [...denominations];

        // 完全清除所有现有的筹码按钮
        $('.chip-btn').remove();

        const clearBtn = domCache.$clearBet || $('#clear-bet');

        if (!clearBtn.length) {
            console.error('Clear button not found');
            return;
        }

        const chipButtonsFragment = document.createDocumentFragment();

        denominations.forEach(value => {
            const formattedValue = formatChipValue(value);
            const chipClass = getChipClass(value, denominations);
            const chipButton = $(`
                <button class="chip-btn" data-value="${value}" title="${formattedValue} chips">
                    <div class="chip ${chipClass}">${formattedValue}</div>
                </button>
            `)[0];
            chipButtonsFragment.appendChild(chipButton);
        });

        // 确保在clear按钮之前插入
        clearBtn[0].parentNode.insertBefore(chipButtonsFragment, clearBtn[0]);

        // 更新缓存
        domCache.$chipButtons = $('.chip-btn');

        bindChipEvents();
        updateChipButtonStates();
    }
    function formatChipValue(value) {
        return value.toLocaleString();
    }
    function getChipClass(value, denominations = null) {
        if (!denominations) {
            denominations = generateChipDenominations(gameState.balance);
        }
        const chipClasses = ['chip-1', 'chip-2', 'chip-3', 'chip-4', 'chip-5', 'chip-6', 'chip-7', 'chip-8', 'chip-9'];
        const position = denominations.indexOf(value);
        return position >= 0 && position < chipClasses.length ? chipClasses[position] : chipClasses[0];
    }
    function getChipPreviewClass(value, denominations = null) {
        if (!denominations) {
            denominations = generateChipDenominations(gameState.balance);
        }
        const chipPreviewClasses = ['chip-1-preview', 'chip-2-preview', 'chip-3-preview', 'chip-4-preview', 'chip-5-preview', 'chip-6-preview', 'chip-7-preview', 'chip-8-preview', 'chip-9-preview'];
        if (value === 50) {
            return chipPreviewClasses[0];
        }
        if (value === 100) {
            return chipPreviewClasses[1];
        }
        const position = denominations.indexOf(value);
        return position >= 0 ? chipPreviewClasses[position] : chipPreviewClasses[0];
    }
    let chipEventsBound = false;

    function bindChipEvents() {
        if (chipEventsBound) {
            return;
        }
        chipEventsBound = true;

        $(document).off('click.chipEvents').on('click.chipEvents', '.chip-btn', function() {
            if (gameState.gameInProgress || !gameState.gameStarted) return;
            const betValue = parseInt($(this).data('value'));
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];

            let actualBetValue = betValue;

            // 如果是最小筹码(5)且余额不足，则下注所有剩余筹码
            if (betValue === 5 && gameState.balance < betValue && gameState.balance > 0) {
                actualBetValue = gameState.balance;
            }

            if (gameState.balance >= actualBetValue && actualBetValue > 0) {
                gameState.balance -= actualBetValue;
                currentPlayer.bet += actualBetValue;
                animateChipToBet(actualBetValue, gameState.currentPlayerIndex);
                updateDisplay();
                safeSetTimeout(() => {
                    updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
                    updateDealButtonState();
                    updateChipButtonStates(); // 确保筹码状态在余额变化后立即更新
                }, 400);
                $(this).addClass('selected');
                safeSetTimeout(() => $(this).removeClass('selected'), 300);
                playSound('chipPlace');
            } else {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalance') : 'Insufficient balance!');
            }
        });
    }

    function unbindChipEvents() {
        $(document).off('click.chipEvents');
        chipEventsBound = false;
    }
    function updateChipDenominationsIfNeeded() {
        if (gameState.gameInProgress || gameState.gameStarted) {
            return;
        }
        const currentDenominations = $('.chip-btn').map(function() {
            return parseInt($(this).data('value'));
        }).get();
        const newDenominations = generateChipDenominations(gameState.balance);
        const hasChanged = currentDenominations.length !== newDenominations.length ||
                          currentDenominations.some((val, index) => val !== newDenominations[index]);
        if (hasChanged) {
            updateChipDenominations();
        }
    }

    function updateChipButtonStates() {
        const balance = gameState.balance;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const gameInProgress = gameState.gameInProgress;
        const gamePhase = gameState.gamePhase;
        const canBet = !gameInProgress && (gamePhase === 'betting' || gamePhase === 'waiting');

        $('.chip-btn').each(function() {
            const chipValue = parseInt($(this).data('value'));
            const canAfford = balance >= chipValue;
            const isMinChip = chipValue === 5; // 最小筹码始终可用
            const isEnabled = canBet && (canAfford || (isMinChip && balance > 0));

            $(this).toggleClass('disabled', !isEnabled).prop('disabled', !isEnabled);

            let title = `${chipValue} chips`;
            if (!canBet) title += ' (Game in progress)';
            else if (!canAfford && isMinChip && balance > 0) title += ` (Bet all ${balance})`;
            else if (!canAfford) title += ' (Insufficient Balance)';

            $(this).attr('title', title);
        });
    }
    function showPlayerScore(playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPosition}`).parent('.player-score').removeClass('hidden');
    }
    function hidePlayerScore(playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        $(`#player-score-${htmlPosition}`).parent('.player-score').addClass('hidden');
    }
    function hideAllPlayerScores() {
        gameState.players.forEach((_, index) => {
            hidePlayerScore(index);
        });
    }
    function displayCards() {
        $('.dealer-cards-area').empty();
        gameState.players.forEach((player, index) => {
            const htmlPosition = getPlayerHtmlPosition(index);
            $(`#player-cards-${htmlPosition}`).empty();
        });
        hideAllPlayerScores();
        gameState.dealerCards.forEach((card, index) => {
            safeSetTimeout(() => {
                if (index === 1 && gameState.gameInProgress && gameState.dealerHiddenCard) {
                    $('.dealer-cards-area').append(createCardElement(card, true));
                } else {
                    $('.dealer-cards-area').append(createCardElement(card));
                }
                playSound('cardDeal');
            }, index * 200);
        });
        gameState.players.forEach((player, playerIndex) => {
            player.cards.forEach((card, cardIndex) => {
                const dealDelay = (gameState.dealerCards.length + playerIndex * 2 + cardIndex) * 200;
                safeSetTimeout(() => {
                    const htmlPosition = getPlayerHtmlPosition(playerIndex);
                    $(`#player-cards-${htmlPosition}`).append(createCardElement(card));
                    playSound('cardDeal');
                }, dealDelay);
            });
            if (player.cards.length > 0) {
                const playerLastCardDelay = (gameState.dealerCards.length + playerIndex * 2 + player.cards.length - 1) * 200;
                safeSetTimeout(() => {
                    showPlayerScore(playerIndex);
                }, playerLastCardDelay + 300);
            }
        });
    }
    function addCardToPlayer(playerIndex, card) {
        gameState.players[playerIndex].cards.push(card);
        gameState.players[playerIndex].score = calculateScore(gameState.players[playerIndex].cards);
        safeSetTimeout(() => {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            $(`#player-cards-${htmlPosition}`).append(createCardElement(card));
            playSound('cardDeal');
            updateDisplay();
            safeSetTimeout(() => {
                showPlayerScore(playerIndex);
                check21PointEffect(playerIndex);
            }, 100);
        }, 200);
    }
    function startNewGame() {
        if (gameState.gameInProgress || gameState.gamePhase === 'dealing') {
            return;
        }

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.bet === 0) {
            updateGameStatus(window.i18n ? window.i18n.t('messages.placeBetFirst') : 'Please place your bet first!');
            return;
        }

        gameState.gamePhase = 'dealing';

        if (gameState.deck.length < 10) {
            triggerShuffleWithAnimation().then(() => {
                continueStartNewGame();
            });
            return;
        }
        continueStartNewGame();
    }
    function continueStartNewGame() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        currentPlayer.isActive = true;
        forceSyncSideBetDisplays();
        stopBettingCountdown();
        gameState.dealerCards = [];
        gameState.gameInProgress = true;
        gameState.dealerHiddenCard = null;
        gameState.gamePhase = 'dealing';
        gameState.currentTurnIndex = -1;
        gameState.players.forEach(player => {
            player.cards = [];
            player.score = 0;
            player.isBust = false;
        });
        updateGameStatus(window.i18n ? window.i18n.t('messages.dealingCards') : 'Dealing cards...');
        hideDealButton();
        $('#betting-controls').fadeOut();
        hideSideBetControls();
        disableGameButtons();
        safeSetTimeout(() => {
            gameState.players.forEach((player, index) => {
                if (player.isActive) {
                    player.cards.push(dealCard());
                }
            });
            gameState.dealerCards.push(dealCard());
            gameState.players.forEach((player, index) => {
                if (player.isActive) {
                    player.cards.push(dealCard());
                }
            });
            gameState.dealerCards.push(dealCard());
            gameState.dealerHiddenCard = gameState.dealerCards[1];
            gameState.players.forEach(player => {
                if (player.isActive) {
                    player.score = calculateScore(player.cards);
                }
            });
            gameState.dealerScore = calculateScore([gameState.dealerCards[0]]);
            displayCards();
            updateDisplay();
            safeSetTimeout(() => {
                gameState.players.forEach((player, index) => {
                    if (player.isActive) {
                        showPlayerScore(index);
                        check21PointEffect(index);
                    }
                });
            }, 2000); 
            safeSetTimeout(() => {
                checkForInsurance();
            }, 2500);
        }, 500);
    }

    function checkForInsurance() {
        // Check if dealer shows an Ace
        if (gameState.dealerCards.length > 0 && gameState.dealerCards[0].value === 'A') {
            // Check if any active player needs insurance (doesn't have blackjack and can afford it)
            const needsInsurance = gameState.players.some(player => {
                if (!player.isActive) return false;
                const hasBlackjack = player.score === 21 && player.cards.length === 2;
                const canAffordInsurance = player.isAI || gameState.balance >= Math.floor(player.bet / 2);
                return !hasBlackjack && canAffordInsurance;
            });

            if (needsInsurance) {
                offerInsurance();
            } else {
                startPlayerTurns();
            }
        } else {
            startPlayerTurns();
        }
    }

    function startPlayerTurns() {
        gameState.gamePhase = 'playing';
        gameState.currentTurnIndex = 0;
        nextPlayerTurn();
    }
    function nextPlayerTurn() {
        while (gameState.currentTurnIndex < gameState.players.length) {
            const player = gameState.players[gameState.currentTurnIndex];
            if (player.isActive && !player.isBust) {
                if (player.score === 21) {
                    showActionBubble(gameState.currentTurnIndex, '21!', 'stand');
                    safeSetTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 1500);
                    return;
                } else if (player.isAI) {
                    setActiveTurn(gameState.currentTurnIndex);
                    handleAITurn(gameState.currentTurnIndex);
                    return;
                } else {
                    setActiveTurn(gameState.currentTurnIndex);
                    updateGameStatus(`Your turn - Hit or Stand?`);
                    enableGameButtons();
                    updateSplitButtonVisibility();
                    return;
                }
            }
            gameState.currentTurnIndex++;
        }
        clearActiveTurn();
        dealerTurn();
    }
    function handleAITurn(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.splitHands.length > 0) {
            const currentHand = player.splitHands[player.currentHandIndex];
            updateGameStatus(`${player.name} is thinking about hand ${player.currentHandIndex + 1}...`);
            safeSetTimeout(() => {
                if (currentHand.score < 17) {
                    showActionBubble(playerIndex, `Hand ${player.currentHandIndex + 1} Hit`, 'hit');
                    safeSetTimeout(() => {
                        const newCard = dealCard();
                        currentHand.cards.push(newCard);
                        currentHand.score = calculateScore(currentHand.cards);
                        displaySplitHands(playerIndex);
                        updateDisplay();
                        if (currentHand.score > 21) {
                            showBustEffect(playerIndex);
                            safeSetTimeout(() => {
                                moveToNextSplitHand(playerIndex, (hasMoreHands) => {
                                    if (!hasMoreHands) {
                                        gameState.currentTurnIndex++;
                                        nextPlayerTurn();
                                    } else {
                                        safeSetTimeout(() => handleAITurn(playerIndex), 1000);
                                    }
                                });
                            }, 1500);
                        } else if (currentHand.score === 21) {
                            show21PointEffect(playerIndex);
                            safeSetTimeout(() => {
                                moveToNextSplitHand(playerIndex, (hasMoreHands) => {
                                    if (!hasMoreHands) {
                                        gameState.currentTurnIndex++;
                                        nextPlayerTurn();
                                    } else {
                                        safeSetTimeout(() => handleAITurn(playerIndex), 1000);
                                    }
                                });
                            }, 1500);
                        } else {
                            safeSetTimeout(() => handleAITurn(playerIndex), 1000);
                        }
                    }, 1000);
                } else {
                    showActionBubble(playerIndex, `Hand ${player.currentHandIndex + 1} Stand`, 'stand');
                    safeSetTimeout(() => {
                        moveToNextSplitHand(playerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                safeSetTimeout(() => handleAITurn(playerIndex), 1000);
                            }
                        });
                    }, 1500);
                }
            }, 1500);
        } else {
            updateGameStatus(`${player.name} is thinking...`);
            safeSetTimeout(() => {
                if (canPlayerSplit(playerIndex) && shouldAISplit(player.cards[0].value)) {
                    showActionBubble(playerIndex, 'Split', 'hit');
                    safeSetTimeout(() => {
                        splitPlayerHand(playerIndex);
                        safeSetTimeout(() => handleAITurn(playerIndex), 1000);
                    }, 1000);
                    return;
                }
                const aiDecision = makeAIDecision(player, gameState.dealerCards[0]);
                if (aiDecision === 'hit') {
                    showActionBubble(playerIndex, 'Hit', 'hit');
                    safeSetTimeout(() => {
                        addCardToPlayer(playerIndex, dealCard());
                        if (player.score > 21) {
                            safeSetTimeout(() => {
                                showBustEffect(playerIndex);
                                safeSetTimeout(() => {
                                    gameState.currentTurnIndex++;
                                    nextPlayerTurn();
                                }, 1500);
                            }, 500);
                        } else if (player.score === 21) {
                            const htmlPosition = getPlayerHtmlPosition(playerIndex);
                            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
                            if (player.cards.length === 2) {
                                playerPosition.addClass('blackjack');
                                showActionBubble(playerIndex, 'BlackJack！', 'stand');
                            } else {
                                playerPosition.addClass('twenty-one');
                                showActionBubble(playerIndex, '21！', 'stand');
                            }
                            safeSetTimeout(() => {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            }, 1500);
                        } else {
                            safeSetTimeout(() => handleAITurn(playerIndex), 1000);
                        }
                    }, 1000);
                } else {
                    showActionBubble(playerIndex, 'Stand', 'stand');
                    safeSetTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 1500);
                }
            }, 1500);
        }
    }
    function makeAIDecision(player, dealerUpCard) {
        const playerScore = player.score;
        const dealerValue = getCardValue(dealerUpCard.value);
        if (playerScore <= 11) {
            return 'hit'; 
        }
        if (playerScore >= 17) {
            if (playerScore === 17 && dealerValue >= 9 && Math.random() < 0.15) {
                return 'hit'; 
            }
            return 'stand'; 
        }
        const hasAce = player.cards.some(card => card.value === 'A');
        if (hasAce && playerScore <= 17) {
            if (dealerValue <= 6) {
                return Math.random() < 0.8 ? 'stand' : 'hit'; 
            } else {
                return Math.random() < 0.7 ? 'hit' : 'stand'; 
            }
        }
        if (playerScore === 12) {
            if (dealerValue >= 4 && dealerValue <= 6) {
                return Math.random() < 0.8 ? 'stand' : 'hit'; 
            } else {
                return Math.random() < 0.75 ? 'hit' : 'stand'; 
            }
        }
        if (playerScore >= 13 && playerScore <= 16) {
            if (dealerValue <= 6) {
                return Math.random() < 0.85 ? 'stand' : 'hit'; 
            } else {
                return Math.random() < 0.8 ? 'hit' : 'stand'; 
            }
        }
        return playerScore < 17 ? 'hit' : 'stand';
    }
    function shouldAISplit(cardValue) {
        switch(cardValue) {
            case 'A':
            case '8':
                return Math.random() < 0.95; 
            case '2':
            case '3':
            case '7':
                return Math.random() < 0.7; 
            case '6':
            case '9':
                return Math.random() < 0.6; 
            case '4':
            case '5':
            case '10':
            case 'J':
            case 'Q':
            case 'K':
                return Math.random() < 0.1; 
            default:
                return Math.random() < 0.4; 
        }
    }
    function getCardValue(cardValue) {
        switch(cardValue) {
            case 'A': return 11; 
            case 'K':
            case 'Q':
            case 'J': return 10;
            default: return parseInt(cardValue);
        }
    }
    function playerHit() {
        if (!gameState.gameInProgress || gameState.currentTurnIndex !== gameState.currentPlayerIndex) return;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const hitText = window.i18n ? window.i18n.t('ui.hit') : 'Hit';
        showActionBubble(gameState.currentPlayerIndex, hitText, 'hit');
        disableGameButtons();
        setTimeout(() => {
            if (currentPlayer.splitHands.length > 0) {
                const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
                const newCard = dealCard();
                currentHand.cards.push(newCard);
                currentHand.score = calculateScore(currentHand.cards);
                playSound('deal');
                const newCardIndex = currentHand.cards.length - 1;
                displaySplitHands(gameState.currentPlayerIndex, newCardIndex);
                updateDisplay();
                if (currentHand.score > 21) {
                    showBustEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                enableGameButtons();
                                updateSplitButtonVisibility();
                            }
                        });
                    }, 1500);
                } else if (currentHand.score === 21) {
                    show21PointEffect(gameState.currentPlayerIndex);
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                enableGameButtons();
                                updateSplitButtonVisibility();
                            }
                        });
                    }, 1500);
                } else {
                    const message = window.i18n ?
                        window.i18n.t('messages.handHitOrStand', { handNumber: currentPlayer.currentHandIndex + 1 }) :
                        `Hand ${currentPlayer.currentHandIndex + 1} - Continue Hit or Stand?`;
                    updateGameStatus(message);
                    enableGameButtons();
                    updateSplitButtonVisibility();
                }
            } else {
                addCardToPlayer(gameState.currentPlayerIndex, dealCard());
                if (currentPlayer.score > 21) {
                    setTimeout(() => {
                        showBustEffect(gameState.currentPlayerIndex);
                        setTimeout(() => {
                            gameState.currentTurnIndex++;
                            nextPlayerTurn();
                        }, 1500);
                    }, 500);
                } else if (currentPlayer.score === 21) {
                    const playerPosition = $(`.player-position[data-position="${gameState.currentPlayerIndex}"]`);
                    if (currentPlayer.cards.length === 2) {
                        playerPosition.addClass('blackjack');
                        showActionBubble(gameState.currentPlayerIndex, 'BlackJack！', 'stand');
                    } else {
                        playerPosition.addClass('twenty-one');
                        showActionBubble(gameState.currentPlayerIndex, '21！', 'stand');
                    }
                    setTimeout(() => {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }, 1500);
                } else {
                    updateGameStatus(window.i18n ? window.i18n.t('messages.continueHitOrStand') : 'Continue Hit or Stand?');
                    enableGameButtons();
                    updateSplitButtonVisibility();
                }
            }
        }, 1000);
    }
    function playerStand() {
        if (!gameState.gameInProgress || gameState.currentTurnIndex !== gameState.currentPlayerIndex) return;
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer.splitHands.length > 0) {
            showActionBubble(gameState.currentPlayerIndex, `Hand${currentPlayer.currentHandIndex + 1}Stand`, 'stand');
        } else {
            const standText = window.i18n ? window.i18n.t('ui.stand') : 'Stand';
            showActionBubble(gameState.currentPlayerIndex, standText, 'stand');
        }
        disableGameButtons();
        setTimeout(() => {
            if (currentPlayer.splitHands.length > 0) {
                moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                    if (!hasMoreHands) {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    } else {
                        enableGameButtons();
                        updateSplitButtonVisibility();
                    }
                });
            } else {
                gameState.currentTurnIndex++;
                nextPlayerTurn();
            }
        }, 1500);
    }
    function revealDealerHiddenCard() {
        const hiddenCardElement = $('.dealer-cards-area .card-back');
        if (hiddenCardElement.length > 0) {
            const revealedCard = createCardElement(gameState.dealerCards[1]);
            hiddenCardElement.replaceWith(revealedCard);
            playSound('cardFlip');
        }
    }
    function dealerTurn() {
        gameState.gamePhase = 'dealer';
        updateGameStatus(window.i18n ? window.i18n.t('messages.dealerTurn') : 'Dealer playing...');
        disableGameButtons();
        gameState.dealerHiddenCard = null;
        gameState.dealerScore = calculateScore(gameState.dealerCards);
        setTimeout(() => {
            revealDealerHiddenCard();
            updateDisplay();
            dealerHitSequence();
        }, 1000);
    }
    function dealerHitSequence() {
        const shouldDealerHit = shouldDealerHitStandard();
        if (shouldDealerHit) {
            setTimeout(() => {
                gameState.dealerCards.push(dealCard());
                gameState.dealerScore = calculateScore(gameState.dealerCards);
                $('.dealer-cards-area').append(createCardElement(gameState.dealerCards[gameState.dealerCards.length - 1]));
                updateDisplay();
                playSound('cardDeal');
                if (gameState.dealerScore === 21) {
                    if (gameState.dealerCards.length === 2) {
                        showDealerEffect('blackjack');
                    } else {
                        showDealerEffect('twenty-one');
                    }
                }
                if (gameState.dealerScore > 21) {
                    showDealerEffect('bust');
                }
                dealerHitSequence();
            }, 1500);
        } else {
            if (gameState.dealerScore > 21) {
                showDealerEffect('bust');
            }
            setTimeout(() => {
                showGameResults();
            }, 1000);
        }
    }
    function shouldDealerHitStandard() {
        if (gameState.dealerScore <= 16) {
            return true;
        }
        if (gameState.dealerScore >= 18) {
            return false;
        }
        if (gameState.dealerScore === 17) {
            return isSoft17(gameState.dealerCards);
        }
        return false;
    }
    function isSoft17(cards) {
        if (!cards || cards.length === 0) return false;
        let aces = 0;
        let otherCardsTotal = 0;
        for (let card of cards) {
            if (card.value === 'A') {
                aces++;
            } else if (['J', 'Q', 'K'].includes(card.value)) {
                otherCardsTotal += 10;
            } else {
                otherCardsTotal += parseInt(card.value);
            }
        }
        return aces >= 1 && (otherCardsTotal + (aces - 1)) === 6;
    }
    function doubleDown() {
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (!gameState.gameInProgress ||
            gameState.currentTurnIndex !== gameState.currentPlayerIndex) {
            return;
        }

        // Check card count for double down eligibility (DAS - Double After Split)
        let currentCards = [];
        let currentBet = currentPlayer.bet;
        if (currentPlayer.splitHands.length > 0) {
            currentCards = currentPlayer.splitHands[currentPlayer.currentHandIndex].cards;
            currentBet = currentPlayer.splitHands[currentPlayer.currentHandIndex].bet;
        } else {
            currentCards = currentPlayer.cards;
        }

        if (currentCards.length !== 2) {
            return;
        }

        if (currentBet > gameState.balance) {
            updateGameStatus('Insufficient balance for double down!');
            return;
        }

        gameState.balance -= currentBet;
        if (currentPlayer.splitHands.length > 0) {
            currentPlayer.splitHands[currentPlayer.currentHandIndex].bet *= 2;
        } else {
            currentPlayer.bet *= 2;
        }
        const doubleText = window.i18n ? window.i18n.t('ui.double') : 'Double Down';
        showActionBubble(gameState.currentPlayerIndex, doubleText, 'hit');
        disableGameButtons();
        setTimeout(() => {
            if (currentPlayer.splitHands.length > 0) {
                // Handle split hand double down
                const currentHand = currentPlayer.splitHands[currentPlayer.currentHandIndex];
                const newCard = dealCard();
                currentHand.cards.push(newCard);
                currentHand.score = calculateScore(currentHand.cards);
                playSound('deal');
                const newCardIndex = currentHand.cards.length - 1;
                displaySplitHands(gameState.currentPlayerIndex, newCardIndex);
                updateDisplay();

                setTimeout(() => {
                    if (currentHand.score > 21) {
                        showBustEffect(gameState.currentPlayerIndex);
                    }
                    setTimeout(() => {
                        moveToNextSplitHand(gameState.currentPlayerIndex, (hasMoreHands) => {
                            if (!hasMoreHands) {
                                gameState.currentTurnIndex++;
                                nextPlayerTurn();
                            } else {
                                enableGameButtons();
                                updateSplitButtonVisibility();
                            }
                        });
                    }, 1000);
                }, 1000);
            } else {
                // Handle regular double down
                addCardToPlayer(gameState.currentPlayerIndex, dealCard());
                setTimeout(() => {
                    if (currentPlayer.score > 21) {
                        showBustEffect(gameState.currentPlayerIndex);
                        setTimeout(() => {
                            gameState.currentTurnIndex++;
                            nextPlayerTurn();
                        }, 1500);
                    } else {
                        gameState.currentTurnIndex++;
                        nextPlayerTurn();
                    }
                }, 1000);
            }
        }, 1000);
        updateDisplay();
    }
    function showGameResults() {
        gameState.gamePhase = 'results';
        updateGameStatus(window.i18n ? window.i18n.t('messages.calculatingResults') : 'Calculating results...');
        let totalWinnings = 0;
        gameState.winSoundPlayed = false;
        gameState.players.forEach((player, index) => {
            if (player.isActive) {
                let totalPlayerWinnings = 0;
                let resultClass = '';
                if (player.splitHands.length > 0) {
                    let handResults = [];
                    player.splitHands.forEach((hand, handIndex) => {
                        let handWinnings = 0;
                        let handResult = '';
                        if (hand.isBust || hand.score > 21) {
                            handResult = 'lost';
                            handWinnings = 0;
                        } else if (hand.score === 21 && hand.cards.length === 2) {
                            handResult = 'blackjack';
                            handWinnings = Math.floor(hand.bet * 2.5);
                        } else if (gameState.dealerScore > 21) {
                            handResult = 'won';
                            handWinnings = hand.bet * 2;
                        } else if (hand.score > gameState.dealerScore) {
                            handResult = 'won';
                            handWinnings = hand.bet * 2;
                        } else if (hand.score === gameState.dealerScore) {
                            handResult = 'push';
                            handWinnings = hand.bet;
                        } else {
                            handResult = 'lost';
                            handWinnings = 0;
                        }
                        handResults.push(handResult);
                        totalPlayerWinnings += handWinnings;
                    });
                    if (handResults.every(result => result === 'lost' || result === 'bust')) {
                        resultClass = 'lost';
                    } else if (handResults.some(result => result === 'won' || result === 'blackjack')) {
                        resultClass = 'won';
                    } else if (handResults.every(result => result === 'push')) {
                        resultClass = 'push';
                    } else {
                        resultClass = 'push'; 
                    }
                    setTimeout(() => {
                        applySplitHandsSettlement(index, handResults);
                    }, 1000);
                } else {
                    if (player.score > 21) {
                        resultClass = 'bust lost';
                        totalPlayerWinnings = 0;
                    } else if (player.score === 21 && player.cards.length === 2) {
                        resultClass = 'blackjack won';
                        totalPlayerWinnings = Math.floor(player.bet * 2.5);
                    } else if (gameState.dealerScore > 21) {
                        resultClass = 'won';
                        totalPlayerWinnings = player.bet * 2;
                    } else if (player.score > gameState.dealerScore) {
                        resultClass = 'won';
                        totalPlayerWinnings = player.bet * 2;
                    } else if (player.score === gameState.dealerScore) {
                        resultClass = 'push';
                        totalPlayerWinnings = player.bet;
                    } else {
                        resultClass = 'lost';
                        totalPlayerWinnings = 0;
                    }
                    const htmlPosition = getPlayerHtmlPosition(index);
                    const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
                    playerPosition.removeClass('bust lost won push blackjack');
                    setTimeout(() => {
                        playerPosition.addClass(resultClass);
                        const mainResult = resultClass.split(' ')[0]; 
                        animateSettlementChips(index, mainResult, totalPlayerWinnings);
                    }, 500);
                }
                if (index === gameState.currentPlayerIndex) {
                    totalWinnings = totalPlayerWinnings;
                }
            }
        });
        if (totalWinnings > 0) {
            gameState.balance += totalWinnings;
            updateDisplay();
        }
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        if (currentPlayer && currentPlayer.isActive) {
            const isWin = totalWinnings > currentPlayer.bet;
            const netWin = totalWinnings - currentPlayer.bet;
            updateAchievements(netWin, isWin);
        }
        cacheUserBalance();
        setTimeout(() => {
            let dealerWon = false;
            let dealerLost = false;
            gameState.players.forEach((player, index) => {
                if (player.isActive) {
                    if (player.score <= 21 && (gameState.dealerScore > 21 || player.score > gameState.dealerScore)) {
                        dealerLost = true;
                    } else if (player.score > 21 || (gameState.dealerScore <= 21 && gameState.dealerScore > player.score)) {
                        dealerWon = true;
                    }
                }
            });
            if (!$('.dealer-section').hasClass('blackjack twenty-one bust')) {
                if (dealerLost && !dealerWon) {
                    showDealerEffect('lost');
                } else if (dealerWon && !dealerLost) {
                    showDealerEffect('won');
                }
            }
        }, 500);
        setTimeout(() => {
            gameState.players.forEach((player, index) => {
                if (player.isActive) {
                    processSideBetWinnings(index);
                }
            });
        }, 1000);
        safeSetTimeout(() => {
            resetForNextGame();
        }, 5000);
    }
    function resetForNextGame() {
        cleanupAnimationElements();
        gameState.dealerCards.forEach(card => {
            if (card) gameState.discardPile.push(card);
        });
        gameState.players.forEach(player => {
            player.cards.forEach(card => {
                if (card) gameState.discardPile.push(card);
            });
            if (player.splitHands && player.splitHands.length > 0) {
                player.splitHands.forEach(hand => {
                    hand.cards.forEach(card => {
                        if (card) gameState.discardPile.push(card);
                    });
                });
            }
        });
        gameState.gameInProgress = false;
        gameState.gamePhase = 'betting';
        gameState.currentTurnIndex = -1;
        stopBettingCountdown();
        gameState.dealerCards = [];
        gameState.dealerScore = 0;
        gameState.dealerHiddenCard = null;
        gameState.playerReady = false;
        gameState.autoStartScheduled = false;
        $('.dealer-section').removeClass('bust lost won blackjack twenty-one');
        gameState.players.forEach((player, index) => {
            player.bet = 0;
            player.isActive = false;
            player.cards = [];
            player.score = 0;
            player.isBust = false;
            player.splitHands = [];
            player.currentHandIndex = 0;
            player.canSplit = false;
            player.sideBets = {
                perfectPairs: 0,
                twentyOnePlusThree: 0
            };
            player.hasInsurance = false;
            player.insuranceBet = 0;
            player.hasInsurance = false;
            player.insuranceBet = 0;
            const htmlPosition = getPlayerHtmlPosition(index);
            $(`.player-position[data-position="${htmlPosition}"]`).removeClass('bust lost won push blackjack twenty-one active-turn');
            $(`#action-bubble-${index}`).removeClass('show hit stand bust');
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-chips-stack`).remove();
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot`).removeClass('has-bet');
            $(`.player-position[data-position="${htmlPosition}"] .side-bet-amount`).text('0');
        });
        updateDeckDisplay();
        $('.dealer-cards-area').empty();
        $('.player-cards-area').empty();
        hideAllPlayerScores();
        updateGameStatus(window.i18n ? window.i18n.t('messages.placeBetsNextRound') : 'Place your bets for next round');
        showDealButton();
        $('#deal-cards').prop('disabled', true);
        $('#action-controls').fadeOut();
        updateDisplay();
        updateChipDenominations(); // 这里调用是必要的，因为游戏结束后需要重新生成筹码
        updateChipButtonStates();
        $('#betting-controls').fadeIn();
        showSideBetControls();
        forceSyncSideBetDisplays();
        gameState.gameStarted = true;
        if (gameState.balance === 0) {
            safeSetTimeout(() => {
                checkGameOverCondition();
            }, 1000);
        } else {
            startBettingCountdown();
        }
    }
    function allIn() {
        if (!gameState.gameInProgress && gameState.gameStarted) {
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (gameState.balance > 0) {
                animateChipToBet(gameState.balance, gameState.currentPlayerIndex);
                currentPlayer.bet += gameState.balance;
                gameState.balance = 0;
                updateDisplay();
                setTimeout(() => {
                    updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
                    updateDealButtonState();
                    updateChipButtonStates();
                }, 400);
                // updateChipDenominationsIfNeeded(); // 移除重复调用
            } else {
                updateGameStatus('No balance to bet!');
            }
        }
    }
    function updateGameStatus(message) {
        if (domCache.$gameStatus) {
            domCache.$gameStatus.text(message);
        } else {
            $('#game-status').text(message);
        }
    }
    function startGame() {
        if (gameState.gameStarted) return;

        if (!gameState.hasAutoFullscreened) {
            gameState.hasAutoFullscreened = true;
            autoFullscreen();
        }

        gameState.achievements.sessionStartBalance = gameState.balance;
        preloadChipImages(); // Preload chip images to prevent loading issues
        initializePlayers();
        createDeck();
        shuffleDeck();
        updatePlayerPositionsDisplay();
        gameState.gameStarted = true;
        gameState.gamePhase = 'betting';
        playBackgroundMusic();
        $('#game-settings-container').fadeOut(500, function() {
            $('#game-status').fadeIn(500);
            $('.dealer-section').addClass('show').fadeIn(500);
            $('.players-area').addClass('show').fadeIn(500);
            $('.bottom-controls').addClass('show').fadeIn(500);
            $('.top-status').fadeIn(500);
            $('.deck-area').fadeIn(500);
            showSideBetControls();
            showAchievementPanel();
        });
        updateChipDenominations();
        $('#betting-controls').show();
        showDealButton();
        $('#deal-cards').prop('disabled', true);
        const playerText = gameSettings.playerCount === 1 ? 'Single Player' : `${gameSettings.playerCount}-player`;
        updateGameStatus(`Welcome to ${playerText} ${gameSettings.deckCount}-deck Blackjack! Place your bets to start (Min: 5).`);
        startBettingCountdown();
    }
    function startBettingCountdown() {
        if (gameState.countdownActive) return;
        gameState.countdownActive = true;
        gameState.bettingCountdown = 15;
        const countdownElement = $('#betting-countdown');
        countdownElement.removeClass('warning critical');
        countdownElement.fadeIn();
        $('#countdown-timer').text(gameState.bettingCountdown);
        $('.countdown-text').text('Please place a bet......');
        if (gameState.countdownTimer) {
            safeClearInterval(gameState.countdownTimer);
        }
        initializeAIBettingSchedule();
        updateDealButtonState();
        gameState.countdownTimer = safeSetInterval(() => {
            gameState.bettingCountdown--;
            $('#countdown-timer').text(gameState.bettingCountdown);
            const countdownElement = $('#betting-countdown');
            processAIBettingAtTime(gameState.bettingCountdown);
            if (gameState.bettingCountdown <= 3) {
                countdownElement.removeClass('warning').addClass('critical');
                playSound('countdownWarning');
            } else if (gameState.bettingCountdown <= 7) {
                countdownElement.removeClass('critical').addClass('warning');
            } else {
                countdownElement.removeClass('warning critical');
            }
            if (gameState.bettingCountdown <= 0) {
                stopBettingCountdown();
                autoBetMinimum();
            }
        }, 1000);
    }
    function stopBettingCountdown() {
        gameState.countdownActive = false;
        if (gameState.countdownTimer) {
            safeClearInterval(gameState.countdownTimer);
            gameState.countdownTimer = null;
        }
        const countdownElement = $('#betting-countdown');
        countdownElement.removeClass('warning critical');
        countdownElement.fadeOut();
    }
    function autoBetMinimum() {
        if (gameState.gameInProgress || gameState.gamePhase === 'dealing') {
            return;
        }

        const currentPlayer = gameState.players[gameState.currentPlayerIndex];
        const denominations = generateChipDenominations(gameState.balance);
        const minimumBet = denominations.length > 0 ? denominations[0] : 10;
        if (currentPlayer.bet === 0 && gameState.balance >= minimumBet) {
            currentPlayer.bet = minimumBet;
            gameState.balance -= minimumBet;
            updateDisplay();
            updateBetDisplay(gameState.currentPlayerIndex, currentPlayer.bet);
            updateGameStatus('Time up! Auto-betting minimum chips');
            updateDealButtonState();
            setTimeout(() => {
                if (!gameState.gameInProgress && gameState.gamePhase !== 'dealing') {
                    startNewGame();
                }
            }, 2000);
        } else if (currentPlayer.bet > 0) {
            setTimeout(() => {
                if (!gameState.gameInProgress && gameState.gamePhase !== 'dealing') {
                    startNewGame();
                }
            }, 1000);
        } else {
            updateGameStatus('Insufficient balance for auto-betting');
        }
    }
    function showActionBubble(playerIndex, message, type = '') {
        const bubble = $(`#action-bubble-${playerIndex}`);
        bubble.removeClass('show hit stand bust');
        bubble.text(message);
        if (type) {
            bubble.addClass(type);
        }
        bubble.addClass('show');
        safeSetTimeout(() => {
            bubble.removeClass('show');
        }, 2000);
    }
    function showBustEffect(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.splitHands && player.splitHands.length > 0) {
            const currentHand = player.splitHands[player.currentHandIndex];
            currentHand.isBust = true;
            currentHand.effectClass = 'bust'; 
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const cardsContainer = $(`#player-cards-${htmlPosition}`);
            const currentHandContainer = cardsContainer.find('.current-hand');
            currentHandContainer.addClass('bust');
            showActionBubble(playerIndex, `Hand ${player.currentHandIndex + 1}Bust！`, 'bust');
        } else {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            playerPosition.addClass('bust');
            showActionBubble(playerIndex, 'Bust', 'stand');
            player.isBust = true;
        }
    }
    function show21PointEffect(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.splitHands && player.splitHands.length > 0) {
            const currentHand = player.splitHands[player.currentHandIndex];
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const cardsContainer = $(`#player-cards-${htmlPosition}`);
            const currentHandContainer = cardsContainer.find('.current-hand');
            currentHandContainer.addClass('twenty-one');
            currentHand.effectClass = 'twenty-one';
            showActionBubble(playerIndex, `Hand ${player.currentHandIndex + 1}：21！`, 'stand');
        } else {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            const isNaturalBlackjack = player.cards.length === 2 &&
                                     calculateScore(player.cards) === 21;
            if (isNaturalBlackjack) {
                playerPosition.addClass('blackjack');
                showActionBubble(playerIndex, 'BlackJack！', 'stand');
            } else {
                playerPosition.addClass('twenty-one');
                showActionBubble(playerIndex, '21！', 'stand');
            }
        }
    }
    function showDealerEffect(effectType) {
        const dealerSection = $('.dealer-section');
        dealerSection.removeClass('bust lost won blackjack twenty-one');
        dealerSection.addClass(effectType);
    }
    function check21PointEffect(playerIndex) {
        const player = gameState.players[playerIndex];
        if (player.score === 21) {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
            if (player.cards.length === 2) {
                playerPosition.addClass('blackjack');
            } else {
                playerPosition.addClass('twenty-one');
            }
        }
    }
    function createFlyingChip(chipClass, startPos, endPos, delay = 0) {
        // Limit concurrent flying chips to prevent memory issues
        const existingChips = $('.flying-chip');
        if (existingChips.length > 20) {
            existingChips.slice(0, 10).remove();
        }

        const chip = $(`<div class="flying-chip ${chipClass}"></div>`);
        chip.css({
            left: startPos.left + 'px',
            top: startPos.top + 'px',
            willChange: 'transform, opacity' // Optimize for animations
        });
        $('body').append(chip);

        safeSetTimeout(() => {
            chip.css({
                transition: 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                left: endPos.left + 'px',
                top: endPos.top + 'px',
                opacity: 0
            });

            safeSetTimeout(() => {
                chip.css('transform', 'scale(1.2) rotate(180deg)');
            }, 150);

            safeSetTimeout(() => {
                chip.css('transform', 'scale(0.8) rotate(360deg)');
            }, 300);

            safeSetTimeout(() => {
                chip.css('willChange', 'auto'); // Clean up optimization
                chip.remove();
            }, 600);
        }, delay);
    }
    function animateChipToBet(chipValue, playerIndex) {
        const chipButton = $(`.chip-btn[data-value="${chipValue}"]`);
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        if (chipButton.length && betCircle.length) {
            const startPos = chipButton.offset();
            const endPos = betCircle.offset();
            const denominations = generateChipDenominations(gameState.balance);
            const chipClass = getChipPreviewClass(chipValue, denominations);
            createFlyingChip(chipClass, startPos, endPos);
        }
    }
    function animateSplitBetChips(totalBet, playerIndex) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        const playerAvatar = $(`.player-position[data-position="${htmlPosition}"] .player-avatar`);
        if (!betCircle.length || !playerAvatar.length) return;
        const chips = calculateChipBreakdown(totalBet);
        const endPos = betCircle.offset();
        const startPos = playerAvatar.offset();
        chips.forEach((chip, index) => {
            setTimeout(() => {
                const chipValue = chip.value * 1000; 
                const denominations = generateChipDenominations(gameState.balance);
                const chipClass = getChipPreviewClass(chipValue, denominations);
                const offsetEndPos = {
                    left: endPos.left + (index * 2),
                    top: endPos.top - (index * 2)
                };
                const offsetStartPos = {
                    left: startPos.left + (index * 3),
                    top: startPos.top + (index * 2)
                };
                createFlyingChip(chipClass, offsetStartPos, offsetEndPos);
            }, index * 100); 
        });
    }
    function animateChipToSideBet(chipValue, playerIndex, betType) {
        const chipButton = $(`.side-bet-btn[data-type="${betType}"][data-value="${chipValue}"]`);
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const sideBetSpot = $(`.player-position[data-position="${htmlPosition}"] .side-bet-spot[data-type="${betType}"]`);
        if (chipButton.length && sideBetSpot.length) {
            const startPos = chipButton.offset();
            const endPos = sideBetSpot.offset();
            const denominations = generateChipDenominations(gameState.balance);
            const chipClass = getChipPreviewClass(chipValue, denominations);
            createFlyingChip(chipClass, startPos, endPos);
        }
    }
    function animateSettlementChips(playerIndex, result, winAmount = 0) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const playerPosition = $(`.player-position[data-position="${htmlPosition}"]`);
        const dealerSection = $('.dealer-section');
        const playerBalanceDisplay = $('#player-balance');
        if (!playerPosition.length || !dealerSection.length) return;
        const playerPos = playerPosition.offset();
        const dealerPos = dealerSection.offset();
        const balancePos = playerBalanceDisplay.length ? playerBalanceDisplay.offset() : playerPos;
        const chipCount = Math.min(Math.floor(winAmount / 10000) || 1, 5);
        if ((result === 'won' || result === 'blackjack') &&
            playerIndex === gameState.currentPlayerIndex &&
            winAmount > 0 &&
            !gameState.winSoundPlayed) {
            gameState.winSoundPlayed = true;
            playSound('win');
            const totalAnimationDuration = 1600;
            setTimeout(() => {
                if (winSound && !winSound.paused) {
                    winSound.pause();
                    winSound.currentTime = 0;
                    if (backgroundMusic && !isMuted && gameState.gameStarted) {
                        setTimeout(() => {
                            backgroundMusic.play().catch(error => {
                            });
                        }, 200);
                    }
                }
            }, totalAnimationDuration);
        }
        for (let i = 0; i < chipCount; i++) {
            const delay = i * 200; 
            const denominations = generateChipDenominations(gameState.balance);
            const randomChipClass = getChipPreviewClass(denominations[Math.floor(Math.random() * denominations.length)], denominations);
            switch (result) {
                case 'won':
                case 'blackjack':
                    createFlyingChip(randomChipClass, dealerPos, playerPos, delay);
                    if (playerIndex === gameState.currentPlayerIndex) {
                        safeSetTimeout(() => {
                            createFlyingChip(randomChipClass, playerPos, balancePos, 0);
                        }, 1000 + delay);
                    }
                    break;
                case 'lost':
                case 'bust':
                    createFlyingChip(randomChipClass, playerPos, dealerPos, delay);
                    break;
                case 'push':
                    break;
            }
        }
    }
    function setActiveTurn(playerIndex) {
        $('.player-position').removeClass('active-turn');
        if (playerIndex >= 0) {
            const htmlPosition = getPlayerHtmlPosition(playerIndex);
            $(`.player-position[data-position="${htmlPosition}"]`).addClass('active-turn');
        }
    }
    function clearActiveTurn() {
        $('.player-position').removeClass('active-turn');
    }
    function showActionControls() {
        $('#betting-controls').fadeOut();
        $('#action-controls').fadeIn();
    }
    function showDealButton() {
        $('#deal-cards').fadeIn();
    }
    function hideDealButton() {
        $('#deal-cards').fadeOut();
    }
    function enableGameButtons() {
        showActionControls();
        $('#hit, #stand').prop('disabled', false);
        const currentPlayer = gameState.players[gameState.currentPlayerIndex];

        // Check card count and bet amount for double down eligibility (DAS - Double After Split)
        let currentCards = [];
        let currentBet = currentPlayer.bet;
        if (currentPlayer.splitHands.length > 0) {
            currentCards = currentPlayer.splitHands[currentPlayer.currentHandIndex].cards;
            currentBet = currentPlayer.splitHands[currentPlayer.currentHandIndex].bet;
        } else {
            currentCards = currentPlayer.cards;
        }

        $('#double-down').prop('disabled', currentCards.length !== 2 || currentBet > gameState.balance);
        $('#split').prop('disabled', !canPlayerSplit(gameState.currentPlayerIndex));
    }
    function disableGameButtons() {
        $('#action-controls').fadeOut();
        $('#hit, #stand, #double-down').prop('disabled', true);
        if (!gameState.gameInProgress) {
            $('#betting-controls').fadeIn();
        }
    }
    function bindSettingsEvents() {
        $('.setting-btn[data-players]').on('click', function() {
            const playerCount = parseInt($(this).data('players'));
            $('.setting-btn[data-players]').removeClass('active');
            $(this).addClass('active');
            gameSettings.playerCount = playerCount;
        });
        $('.setting-btn[data-decks]').on('click', function() {
            const deckCount = parseInt($(this).data('decks'));
            $('.setting-btn[data-decks]').removeClass('active');
            $(this).addClass('active');
            gameSettings.deckCount = deckCount;
        });
    }
    function bindEvents() {
        $('#settings-button').on('click', function() {
            showUnifiedSettingsModal('blackjack', {
                hasBackgroundMusic: true,
                hasEffects: true,
                hasGameSettings: false,
                hasLanguageSwitch: true,
                hasRules: true,
                hasNavigation: true,
                hasShare: false
            });
        });
        $('#rules-button').on('click', function() {
            showRulesModal();
        });
        $('#rules-close').on('click', function() {
            hideRulesModal();
        });
        $('#rules-modal').on('click', function(e) {
            if (e.target === this) {
                hideRulesModal();
            }
        });
        $('#restart-game-btn').on('click', function() {
            restartGame();
        });
        $('#game-over-modal').on('click', function(e) {
            if (e.target === this) {
            }
        });
        $('#buy-insurance').on('click', function() {
            buyInsurance();
        });
        $('#decline-insurance').on('click', function() {
            declineInsurance();
        });
        $('#rules-close').on('click', function() {
            $('#rules-modal').fadeOut(300);
        });
        $('.rules-button, .rules-btn').on('click', function() {
            $('#rules-modal').fadeIn(300);
        });
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $('#rules-modal').is(':visible')) {
                hideRulesModal();
            }
        });
        $('#start-game-btn').on('click', function() {
            const isFullscreen = document.fullscreenElement ||
                               document.webkitFullscreenElement ||
                               document.mozFullScreenElement ||
                               document.msFullscreenElement;

            if (!gameState.gameStarted && !isFullscreen) {
                const docEl = document.documentElement;
                const requestFullscreen = docEl.requestFullscreen ||
                                        docEl.webkitRequestFullscreen ||
                                        docEl.mozRequestFullScreen ||
                                        docEl.msRequestFullscreen;

                if (requestFullscreen) {
                    requestFullscreen.call(docEl).then(() => {
                        updateFullscreenButton(true);
                        setTimeout(lockScreenOrientation, 200);
                        startGame();
                    }).catch(err => {
                        startGame();
                    });
                } else {
                    startGame();
                }
            } else {
                startGame();
            }
        });
        // bindChipEvents(); // 移除重复调用，已在updateChipDenominations中调用
        $('#clear-bet').on('click', function() {
            if (!gameState.gameInProgress && gameState.gameStarted) {
                const currentPlayer = gameState.players[gameState.currentPlayerIndex];
                gameState.balance += currentPlayer.bet;
                currentPlayer.bet = 0;
                updateDisplay();
                updateBetDisplay(gameState.currentPlayerIndex, 0);
                updateChipButtonStates();
                updateGameStatus(window.i18n ? window.i18n.t('messages.betCleared') : 'Bet cleared - Please place new bet');
                $('#deal-cards').prop('disabled', true);
                startBettingCountdown();
            }
        });
        $('#all-in').on('click', function() {
            if (gameState.gameStarted) {
                allIn();
                $('#deal-cards').prop('disabled', false);
            }
        });
        $('#deal-cards').on('click', function() {
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (currentPlayer.bet > 0 && !gameState.gameInProgress && gameState.gameStarted && !gameState.playerReady) {
                gameState.playerReady = true;
                updateDealButtonState();
            }
        });
        $('.bet-circle').on('click', function() {
            const htmlPosition = parseInt($(this).data('position'));
            let playerIndex = -1;
            for (let i = 0; i < gameSettings.playerCount; i++) {
                if (getPlayerHtmlPosition(i) === htmlPosition) {
                    playerIndex = i;
                    break;
                }
            }
            if (playerIndex === gameState.currentPlayerIndex &&
                gameState.players[playerIndex].bet > 0 &&
                !gameState.gameInProgress &&
                gameState.gamePhase !== 'dealing') {
                startNewGame();
            }
        });
        $('#hit').on('click', playerHit);
        $('#stand').on('click', playerStand);
        $('#double-down').on('click', doubleDown);
        $('#split').on('click', function() {
            if (canPlayerSplit(gameState.currentPlayerIndex)) {
                splitPlayerHand(gameState.currentPlayerIndex);
                updateSplitButtonVisibility();
            }
        });
        $('.side-bet-btn').on('click', function() {
            if (gameState.gameInProgress || !gameState.gameStarted) return;
            const betType = $(this).data('type');
            const betValue = parseInt($(this).data('value'));
            const currentPlayer = gameState.players[gameState.currentPlayerIndex];
            if (gameState.balance >= betValue && betValue > 0) {
                gameState.balance -= betValue;
                currentPlayer.sideBets[betType] += betValue;
                animateChipToSideBet(betValue, gameState.currentPlayerIndex, betType);
                updateDisplay();
                setTimeout(() => {
                    updatePlayerSideBetDisplay(gameState.currentPlayerIndex);
                    $('#perfect-pairs-bet').text(formatBetAmount(currentPlayer.sideBets.perfectPairs));
                    $('#twenty-one-plus-three-bet').text(formatBetAmount(currentPlayer.sideBets.twentyOnePlusThree));
                }, 400);
                $(this).addClass('selected');
                setTimeout(() => $(this).removeClass('selected'), 300);
                playSound('chipPlace');
            } else {
                updateGameStatus(window.i18n ? window.i18n.t('messages.insufficientBalance') : 'Insufficient balance!');
            }
        });
        $('#clear-side-bets').on('click', function() {
            if (!gameState.gameInProgress && gameState.gameStarted) {
                const currentPlayer = gameState.players[gameState.currentPlayerIndex];
                Object.values(currentPlayer.sideBets).forEach(amount => {
                    gameState.balance += amount;
                });
                currentPlayer.sideBets = {
                    perfectPairs: 0,
                    twentyOnePlusThree: 0
                };
                updateDisplay();
                updatePlayerSideBetDisplay(gameState.currentPlayerIndex);
                $('#perfect-pairs-bet').text('0');
                $('#twenty-one-plus-three-bet').text('0');
                updateGameStatus(window.i18n ? window.i18n.t('messages.sideBetsCleared') : 'Side bets cleared');
            }
        });
        $(document).on('keydown', function(e) {
            if ($('#result-modal').is(':visible')) return;
            switch(e.key.toLowerCase()) {
                case ' ':
                case 'enter':
                    e.preventDefault();
                    if (!gameState.gameInProgress && gameState.currentBet > 0 && gameState.gamePhase !== 'dealing') {
                        startNewGame();
                    }
                    break;
                case 'h':
                    if (gameState.gameInProgress && !$('#hit').prop('disabled')) {
                        playerHit();
                    }
                    break;
                case 's':
                    if (gameState.gameInProgress && !$('#stand').prop('disabled')) {
                        playerStand();
                    }
                    break;
                case 'd':
                    if (gameState.gameInProgress && !$('#double-down').prop('disabled')) {
                        doubleDown();
                    }
                    break;
                case 'c':
                    $('#clear-bet').click();
                    break;
                case '1':
                    $('.chip-btn[data-value="10000"]').click();
                    break;
                case '2':
                    $('.chip-btn[data-value="20000"]').click();
                    break;
                case '3':
                    $('.chip-btn[data-value="50000"]').click();
                    break;
                case '4':
                    $('.chip-btn[data-value="100000"]').click();
                    break;
                case '5':
                    $('.chip-btn[data-value="250000"]').click();
                    break;
                case '6':
                    $('.chip-btn[data-value="500000"]').click();
                    break;
                case '7':
                    $('.chip-btn[data-value="1000000"]').click();
                    break;
            }
        });
    }
    function initAchievements() {
        loadAchievements();
        loadUserBalance();
        updateDisplay();
        updateAchievementDisplay();
        showAchievementPanel();
    }
    // 内存监控和优化
    let memoryCheckInterval = null;

    function startMemoryMonitoring() {
        if (memoryCheckInterval) {
            return;
        }

        memoryCheckInterval = safeSetInterval(() => {
            try {
                // 检查内存使用情况
                if (performance.memory) {
                    const memInfo = performance.memory;
                    const usedMB = Math.round(memInfo.usedJSHeapSize / 1048576);
                    const limitMB = Math.round(memInfo.jsHeapSizeLimit / 1048576);

                    // 如果内存使用超过80%，进行清理
                    if (usedMB / limitMB > 0.8) {
                        console.warn('High memory usage detected, performing cleanup');
                        cleanupAnimationElements();

                        // 强制垃圾回收
                        if (window.gc && typeof window.gc === 'function') {
                            window.gc();
                        }
                    }
                }

                // 检查定时器数量
                const totalTimers = activeTimers.timeouts.size + activeTimers.intervals.size + activeTimers.animationFrames.size;
                if (totalTimers > 50) {
                    console.warn('Too many active timers:', totalTimers);
                    clearOldTimers();
                }

                // 检查DOM元素数量
                const animationElements = $('.flying-chip, .action-bubble, [class*="animate-"]').length;
                if (animationElements > 20) {
                    console.warn('Too many animation elements:', animationElements);
                    scheduleAnimationCleanup();
                }

            } catch (error) {
                console.error('Error in memory monitoring:', error);
            }
        }, 30000); // 每30秒检查一次
    }

    function stopMemoryMonitoring() {
        if (memoryCheckInterval) {
            safeClearInterval(memoryCheckInterval);
            memoryCheckInterval = null;
        }
    }

    initDOMCache();
    initGame();
    initAudio();
    disableGameButtons();
    initAchievements();
    startMemoryMonitoring();
    $('#action-controls').hide();
    $('#betting-controls').hide();
    $('#deal-cards').hide();
    $('#game-status').hide();
    $('.top-status').hide();
    $('.deck-area').hide();
    $('.dealer-section').removeClass('show');
    $('.players-area').removeClass('show');
    $('.bottom-controls').removeClass('show');
    $('#game-settings-container').show();
    $('#loading-overlay').fadeOut(1000);
    let audioEnabled = false;
    function enableAudio() {
        if (!audioEnabled && backgroundMusic) {
            audioEnabled = true;
            if (gameState.gameStarted) {
                playBackgroundMusic();
            }
        }
    }
    $(document).one('click touchstart keydown', enableAudio);
    function performFullCleanup() {
        try {
            // 停止内存监控
            stopMemoryMonitoring();

            // 清理定时器
            clearAllTimers();

            // 清理动画元素
            cleanupAnimationElements();

            // 清理音频资源
            cleanupAudio();

            // 清理事件监听器
            unbindChipEvents();
            $(document).off('.chipEvents .gameEvents .audioEvents');
            $(window).off('orientationchange.game resize.game');

            // 清理DOM缓存
            Object.keys(domCache).forEach(key => {
                if (domCache[key] && typeof domCache[key].off === 'function') {
                    domCache[key].off();
                }
                domCache[key] = null;
            });

            // 清理全局变量
            gameState.players = [];
            gameState.deck = [];
            gameState.discardPile = [];
            gameState.dealerCards = [];
            gameState.aiBettingSchedule = [];
            currentChipDenominations = [];

            // 重置标志
            chipEventsBound = false;
            animationCleanupScheduled = false;
            audioInitialized = false;

            console.log('Full cleanup completed');
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }

    $(window).on('beforeunload', performFullCleanup);
    $(window).on('unload', performFullCleanup);

    // 页面隐藏时也进行部分清理
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // 页面隐藏时暂停音频和清理动画
            if (backgroundMusic && !backgroundMusic.paused) {
                backgroundMusic.pause();
            }
            cleanupAnimationElements();
        }
    });
    function handleOrientationChange() {
        const isMobile = window.innerWidth <= 1024;
        const isPortrait = window.innerHeight > window.innerWidth;

        if (isMobile && isPortrait) {
            // 移动端竖屏：显示横屏提示
            $('#rotate-prompt').css('display', 'flex');
            $('.casino-container').css('display', 'none');
        } else if (isMobile && !isPortrait) {
            // 移动端横屏：显示游戏内容
            $('#rotate-prompt').css('display', 'none');
            $('.casino-container').css('display', 'block');
        } else {
            // 桌面端：始终显示游戏内容
            $('#rotate-prompt').css('display', 'none');
            $('.casino-container').css('display', 'block');
        }
    }
    function lockScreenOrientation() {
        let targetOrientation = 'landscape';

        if (screen.orientation) {
            const currentOrientation = screen.orientation.type;
            if (currentOrientation.includes('landscape')) {
                targetOrientation = currentOrientation;
            } else {
                targetOrientation = 'landscape-primary';
            }
        }

        if (screen.orientation && screen.orientation.lock) {
            screen.orientation.lock(targetOrientation).catch(err => {
                console.log('Screen orientation lock failed:', err);
            });
        }
        else if (screen.lockOrientation) {
            screen.lockOrientation(targetOrientation);
        }
        else if (screen.mozLockOrientation) {
            screen.mozLockOrientation(targetOrientation);
        }
        else if (screen.msLockOrientation) {
            screen.msLockOrientation(targetOrientation);
        }

        setTimeout(handleOrientationChange, 100);
    }
    function initOrientationHandling() {
        handleOrientationChange();
        $(window).on('orientationchange resize', function() {
            setTimeout(handleOrientationChange, 100);
        });
        if (screen.orientation) {
            screen.orientation.addEventListener('change', handleOrientationChange);
        }
    }
    $(document).one('touchstart click', function() {
        lockScreenOrientation();
    });
    initOrientationHandling();
    function handleFullscreenChange() {
        const isFullscreen = !!(document.fullscreenElement ||
                               document.webkitFullscreenElement ||
                               document.mozFullScreenElement ||
                               document.msFullscreenElement);
        updateFullscreenButton(isFullscreen);
        setNonGameSectionsVisibility(!isFullscreen);
        ensureFullScreenCoverage();
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    const isFullscreen = !!(document.fullscreenElement ||
                           document.webkitFullscreenElement ||
                           document.mozFullScreenElement ||
                           document.msFullscreenElement);
    updateFullscreenButton(isFullscreen);
    window.resetBalance = resetBalance;
    window.ensureBalanceIntegrity = ensureBalanceIntegrity;
    window.openAudioSettings = openAudioSettings;

    function loadAudioSettings() {
        try {
            const savedMusicVolume = localStorage.getItem('blackjack-music-volume');
            const savedEffectsVolume = localStorage.getItem('blackjack-effects-volume');
            const savedMusicMuted = localStorage.getItem('blackjack-music-muted');
            const savedEffectsMuted = localStorage.getItem('blackjack-effects-muted');

            if (savedMusicVolume !== null) {
                musicVolume = parseFloat(savedMusicVolume);
            }
            if (savedEffectsVolume !== null) {
                effectsVolume = parseFloat(savedEffectsVolume);
            }
            if (savedMusicMuted !== null) {
                musicMuted = savedMusicMuted === 'true';
            }
            if (savedEffectsMuted !== null) {
                effectsMuted = savedEffectsMuted === 'true';
            }

            updateAudioVolumes();
        } catch (error) {
            console.warn('Failed to load audio settings:', error);
        }
    }

    function saveAudioSettings() {
        try {
            localStorage.setItem('blackjack-music-volume', musicVolume.toString());
            localStorage.setItem('blackjack-effects-volume', effectsVolume.toString());
            localStorage.setItem('blackjack-music-muted', musicMuted.toString());
            localStorage.setItem('blackjack-effects-muted', effectsMuted.toString());
        } catch (error) {
            console.warn('Failed to save audio settings:', error);
        }
    }

    function updateAudioVolumes() {
        if (backgroundMusic) {
            backgroundMusic.volume = musicMuted ? 0 : musicVolume;
        }
        if (dealSound) {
            dealSound.volume = effectsMuted ? 0 : effectsVolume;
        }
        if (winSound) {
            winSound.volume = effectsMuted ? 0 : effectsVolume;
        }
    }

    function createAudioSettingsModal() {
        const audioTitle = window.i18n ? window.i18n.t('audio.title') : 'Audio Settings';
        const backgroundMusic = window.i18n ? window.i18n.t('audio.backgroundMusic') : 'Background Music';
        const soundEffects = window.i18n ? window.i18n.t('audio.soundEffects') : 'Sound Effects';
        const volume = window.i18n ? window.i18n.t('audio.volume') : 'Volume';
        const testAudio = window.i18n ? window.i18n.t('audio.testAudio') : 'Test Audio';
        const testMusic = window.i18n ? window.i18n.t('audio.testMusic') : 'Test Music';
        const testEffects = window.i18n ? window.i18n.t('audio.testEffects') : 'Test Effects';

        const modalHTML = `
            <div id="audio-settings-modal" class="audio-modal-overlay">
                <div class="audio-modal-content">
                    <div class="audio-modal-header">
                        <h3 class="audio-modal-title">${audioTitle}</h3>
                        <button id="close-audio-settings" class="audio-close-btn">&times;</button>
                    </div>
                    <div class="audio-modal-body">
                        <div class="audio-control-group">
                            <div class="audio-control-item">
                                <div class="audio-control-header">
                                    <div class="audio-control-label">
                                        <span class="audio-icon">🎵</span>
                                        <span class="audio-text">${backgroundMusic}</span>
                                    </div>
                                    <button id="music-toggle" class="audio-toggle-btn">
                                        <span class="toggle-icon">🔊</span>
                                    </button>
                                </div>
                                <div class="volume-control">
                                    <span class="volume-label">${volume}</span>
                                    <input type="range" id="music-volume" min="0" max="100" value="20" class="volume-slider">
                                    <span id="music-volume-value" class="volume-value">20%</span>
                                </div>
                            </div>

                            <div class="audio-control-item">
                                <div class="audio-control-header">
                                    <div class="audio-control-label">
                                        <span class="audio-icon">🔊</span>
                                        <span class="audio-text">${soundEffects}</span>
                                    </div>
                                    <button id="effects-toggle" class="audio-toggle-btn">
                                        <span class="toggle-icon">🔊</span>
                                    </button>
                                </div>
                                <div class="volume-control">
                                    <span class="volume-label">${volume}</span>
                                    <input type="range" id="effects-volume" min="0" max="100" value="80" class="volume-slider">
                                    <span id="effects-volume-value" class="volume-value">80%</span>
                                </div>
                            </div>
                        </div>

                        <div class="audio-test-section">
                            <h4 class="test-title">${testAudio}</h4>
                            <div class="test-buttons">
                                <button id="test-music" class="test-btn">${testMusic}</button>
                                <button id="test-effects" class="test-btn">${testEffects}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHTML);

        // Slider styles are now in CSS file
        setupAudioSettingsEvents();
    }

    function setupAudioSettingsEvents() {
        $('#close-audio-settings').on('click', function() {
            $('#audio-settings-modal').hide();
        });

        $('#audio-settings-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });

        $('#music-toggle').on('click', function() {
            musicMuted = !musicMuted;
            updateMusicToggleButton();
            updateAudioVolumes();
            saveAudioSettings();
        });

        $('#effects-toggle').on('click', function() {
            effectsMuted = !effectsMuted;
            updateEffectsToggleButton();
            updateAudioVolumes();
            saveAudioSettings();
        });

        $('#music-volume').on('input', function() {
            musicVolume = parseFloat($(this).val()) / 100;
            $('#music-volume-value').text($(this).val() + '%');
            updateAudioVolumes();
            saveAudioSettings();
        });

        $('#effects-volume').on('input', function() {
            effectsVolume = parseFloat($(this).val()) / 100;
            $('#effects-volume-value').text($(this).val() + '%');
            updateAudioVolumes();
            saveAudioSettings();
        });

        $('#test-music').on('click', function() {
            if (backgroundMusic && !musicMuted) {
                backgroundMusic.currentTime = 0;
                backgroundMusic.play().catch(() => {});
                setTimeout(() => {
                    backgroundMusic.pause();
                }, 3000);
            }
        });

        $('#test-effects').on('click', function() {
            if (dealSound && !effectsMuted) {
                dealSound.currentTime = 0;
                dealSound.play().catch(() => {});
            }
        });

        updateAudioSettingsUI();
    }

    function updateMusicToggleButton() {
        const button = $('#music-toggle');
        const icon = button.find('.toggle-icon');

        if (musicMuted) {
            button.css({
                'background': 'rgba(239,68,68,0.2)',
                'border-color': 'rgba(239,68,68,0.4)'
            });
            icon.text('🔇');
        } else {
            button.css({
                'background': 'rgba(34,197,94,0.2)',
                'border-color': 'rgba(34,197,94,0.4)'
            });
            icon.text('🔊');
        }
    }

    function updateEffectsToggleButton() {
        const button = $('#effects-toggle');
        const icon = button.find('.toggle-icon');

        if (effectsMuted) {
            button.css({
                'background': 'rgba(239,68,68,0.2)',
                'border-color': 'rgba(239,68,68,0.4)'
            });
            icon.text('🔇');
        } else {
            button.css({
                'background': 'rgba(34,197,94,0.2)',
                'border-color': 'rgba(34,197,94,0.4)'
            });
            icon.text('🔊');
        }
    }

    function updateAudioSettingsUI() {
        $('#music-volume').val(Math.round(musicVolume * 100));
        $('#music-volume-value').text(Math.round(musicVolume * 100) + '%');
        $('#effects-volume').val(Math.round(effectsVolume * 100));
        $('#effects-volume-value').text(Math.round(effectsVolume * 100) + '%');

        updateMusicToggleButton();
        updateEffectsToggleButton();
    }
    function showRulesModal() {
        $('#rules-modal').fadeIn(300);
        $('body').css('overflow', 'hidden');
    }
    function hideRulesModal() {
        $('#rules-modal').fadeOut(300);
        $('body').css('overflow-y', 'auto');
    }

    // 暴露到全局作用域
    window.showRulesModal = showRulesModal;
    window.hideRulesModal = hideRulesModal;
    function loadAchievements() {
        const saved = localStorage.getItem('blackjack-achievements');
        if (saved) {
            try {
                const achievements = JSON.parse(saved);
                gameState.achievements = { ...gameState.achievements, ...achievements };
            } catch (error) {
            }
        }
    }
    function loadUserBalance() {
        const savedBalance = localStorage.getItem('blackjack-balance');
        if (savedBalance) {
            try {
                const balance = parseInt(savedBalance);
                if (balance > 0) {
                    gameState.balance = balance;
                    gameState.balanceCache = balance;
                    if (!gameState.achievements.sessionStartBalanceCache || gameState.achievements.sessionStartBalanceCache === 1000) {
                        gameState.achievements.sessionStartBalanceCache = balance;
                    }
                    console.log('Loaded cached balance:', balance);
                }
            } catch (error) {
                console.warn('Failed to load cached balance:', error);
            }
        }
    }
    function cacheUserBalance() {
        gameState.balanceCache = gameState.balance;
        localStorage.setItem('blackjack-balance', gameState.balance.toString());
        console.log('Cached user balance:', gameState.balance);
    }
    function resetBalanceCache() {
        gameState.balance = 1000;
        gameState.balanceCache = 1000;
        gameState.achievements.sessionStartBalance = 1000;
        gameState.achievements.sessionStartBalanceCache = 1000;
        localStorage.setItem('blackjack-balance', '1000');
        updateChipButtonStates();
        console.log('Reset balance cache to 1000');
    }
    function saveAchievements() {
        localStorage.setItem('blackjack-achievements', JSON.stringify(gameState.achievements));
    }
    function updateAchievements(winAmount, isWin) {
        gameState.achievements.totalGames++;
        if (isWin) {
            gameState.achievements.totalWins++;
            if (winAmount > gameState.achievements.highestWin) {
                gameState.achievements.highestWin = winAmount;
            }
            gameState.achievements.currentWinStreak++;
            if (gameState.achievements.currentWinStreak > gameState.achievements.bestWinStreak) {
                gameState.achievements.bestWinStreak = gameState.achievements.currentWinStreak;
            }
        } else {
            gameState.achievements.currentWinStreak = 0;
        }
        saveAchievements();
        updateAchievementDisplay();
    }
    function formatAchievementValue(value) {
        return value.toLocaleString();
    }
    function updateAchievementDisplay() {
        $('#highest-win').text(formatAchievementValue(gameState.achievements.highestWin));
        const winRate = gameState.achievements.totalGames > 0
            ? Math.round((gameState.achievements.totalWins / gameState.achievements.totalGames) * 100)
            : 0;
        $('#win-rate').text(winRate + '%');
        const netProfit = gameState.balance - gameState.achievements.sessionStartBalanceCache;
        const netProfitText = netProfit >= 0 ? '+' + formatAchievementValue(netProfit) : formatAchievementValue(netProfit);
        const netProfitElement = $('#net-profit');
        netProfitElement.text(netProfitText);
        netProfitElement.removeClass('profit loss');
        if (netProfit > 0) {
            netProfitElement.addClass('profit');
        } else if (netProfit < 0) {
            netProfitElement.addClass('loss');
        }
        $('#best-streak').text(gameState.achievements.bestWinStreak);
        $('#total-games').text(gameState.achievements.totalGames);
    }
    function showAchievementPanel() {
        $('#achievement-panel').fadeIn(500);
    }
    function resetBalance() {
        gameState.balance = 1000;
        gameState.achievements.sessionStartBalance = 1000;
        updateDisplay();
        updateChipButtonStates();
        updateAchievementDisplay();
    }
    function logBalanceChange(operation, amount, before, after) {
        console.log(`Balance change - Operation: ${operation}, Amount: ${amount}, Before: ${before}, After: ${after}`);
    }

    function deductBalance(amount, operation = 'unknown') {
        const before = gameState.balance;
        if (gameState.balance >= amount && amount > 0) {
            gameState.balance -= amount;
            const after = gameState.balance;
            logBalanceChange(operation, amount, before, after);
            return true;
        } else {
            console.warn(`Failed to deduct ${amount} for ${operation}. Current balance: ${gameState.balance}`);
            return false;
        }
    }
    function updateDealButtonState() {
        const dealButton = $('#deal-cards');
        const humanPlayer = gameState.players[gameState.currentPlayerIndex];
        const pendingAIBets = gameState.aiBettingSchedule.filter(bet => !bet.executed && !bet.isSideBet);
        if (humanPlayer.bet > 0 && !gameState.playerReady) {
            dealButton.prop('disabled', false);
            dealButton.find('.btn-text').text(window.i18n ? window.i18n.t('ui.ready') : 'Ready');
            updateGameStatus(window.i18n ? window.i18n.t('messages.clickDoneToStart') : 'Click Ready when you want to start');
        } else if (gameState.playerReady) {
            dealButton.prop('disabled', true);
            dealButton.find('.btn-text').text(window.i18n ? window.i18n.t('ui.waiting') : 'Waiting...');
            if (pendingAIBets.length > 0) {
                updateGameStatus(window.i18n ? window.i18n.t('messages.welcomeBack') : 'Waiting for other players to place bets...');
            } else {
                updateGameStatus(window.i18n ? window.i18n.t('messages.dealingCards') : 'Starting game...');
                if (!gameState.autoStartScheduled) {
                    gameState.autoStartScheduled = true;
                    setTimeout(() => {
                        const currentPendingBets = gameState.aiBettingSchedule.filter(bet => !bet.executed && !bet.isSideBet);
                        if (gameState.playerReady && currentPendingBets.length === 0) {
                            startDealingCards();
                        }
                    }, 1000);
                }
            }
        } else {
            dealButton.prop('disabled', true);
            dealButton.find('.btn-text').text(window.i18n ? window.i18n.t('ui.deal') : 'Deal');
            updateGameStatus(window.i18n ? window.i18n.t('messages.placeBetToStart') : 'Place your bet to continue');
        }
    }
    function startDealingCards() {
        if (gameState.gameInProgress || gameState.gamePhase === 'dealing') {
            return;
        }

        gameState.playerReady = false;
        gameState.autoStartScheduled = false;
        startNewGame();
    }
    function initializeAIBettingSchedule() {
        gameState.aiBettingSchedule = [];
        gameState.players.forEach((player, index) => {
            if (player.isAI) {
                player.bet = 0;
                player.sideBets = {
                    perfectPairs: 0,
                    twentyOnePlusThree: 0
                };
                const bettingTimes = generateAIBettingTimes();
                const chipDenominations = generateChipDenominations(gameState.balance);
                const numChips = Math.floor(Math.random() * 3) + 1;
                let totalBet = 0;
                for (let i = 0; i < numChips; i++) {
                    const randomChip = chipDenominations[Math.floor(Math.random() * chipDenominations.length)];
                    totalBet += randomChip;
                }
                if (bettingTimes.length > 0) {
                    gameState.aiBettingSchedule.push({
                        playerIndex: index,
                        betTime: bettingTimes[0],
                        betAmount: totalBet,
                        executed: false
                    });
                }
                if (Math.random() < 0.3) {
                    const sideBetDenominations = [50, 100];
                    const sideBetAmount = sideBetDenominations[Math.floor(Math.random() * sideBetDenominations.length)];
                    const sideBetType = Math.random() < 0.5 ? 'perfectPairs' : 'twentyOnePlusThree';
                    const sideBetTime = bettingTimes[Math.min(1, bettingTimes.length - 1)] || bettingTimes[0];
                    gameState.aiBettingSchedule.push({
                        playerIndex: index,
                        betTime: sideBetTime,
                        betAmount: sideBetAmount,
                        betType: sideBetType,
                        isSideBet: true,
                        executed: false
                    });
                }
            }
        });
    }
    function generateAIBettingTimes() {
        const times = [];
        const numBets = Math.floor(Math.random() * 2) + 1;
        for (let i = 0; i < numBets; i++) {
            const time = Math.floor(Math.random() * 10) + 5;
            times.push(time);
        }
        return times.sort((a, b) => b - a);
    }
    function processAIBettingAtTime(currentTime) {
        gameState.aiBettingSchedule.forEach(bet => {
            if (!bet.executed && bet.betTime === currentTime) {
                bet.executed = true;
                if (bet.isSideBet) {
                    const player = gameState.players[bet.playerIndex];
                    player.sideBets[bet.betType] += bet.betAmount;
                    updatePlayerSideBetDisplay(bet.playerIndex);
                } else {
                    const player = gameState.players[bet.playerIndex];
                    player.bet += bet.betAmount;
                    player.isActive = true;
                    animateAIChipPlacement(bet.playerIndex, bet.betAmount);
                    updateBetDisplay(bet.playerIndex, player.bet);
                }
            }
        });
        updateDealButtonState();
    }
    function animateAIChipPlacement(playerIndex, betAmount) {
        const htmlPosition = getPlayerHtmlPosition(playerIndex);
        const playerAvatar = $(`.player-position[data-position="${htmlPosition}"] .player-avatar`);
        const betCircle = $(`.bet-circle[data-position="${htmlPosition}"]`);
        if (playerAvatar.length && betCircle.length) {
            const startPos = playerAvatar.offset();
            const endPos = betCircle.offset();
            const denominations = generateChipDenominations(gameState.balance);
            const chipClass = getChipPreviewClass(betAmount, denominations);
            createFlyingChip(chipClass, startPos, endPos);
            playSound('chipPlace');
        }
    }

    function autoFullscreen() {
        if (!document.fullscreenElement &&
            !document.webkitFullscreenElement &&
            !document.mozFullScreenElement &&
            !document.msFullscreenElement) {

            const element = document.documentElement;
            if (element.requestFullscreen) {
                element.requestFullscreen().catch(() => {});
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        }
    }

    // 语言切换功能现在由统一设置弹窗处理
    function setupLanguageSwitcher() {
        // 监听语言变化事件
        window.addEventListener('languageChanged', function(event) {
            updateDynamicTexts();
        });
    }

    // 更新动态文字
    function updateDynamicTexts() {
        // 更新玩家名称
        $('.player-name').each(function() {
            const $this = $(this);
            const originalText = $this.text();
            if (originalText === 'you' || originalText === 'Sie') {
                $this.text(window.i18n ? window.i18n.t('ui.you') : 'you');
            } else if (originalText.includes('player') || originalText.includes('Spieler')) {
                const playerNumber = originalText.match(/\d+/);
                if (playerNumber) {
                    const playerText = window.i18n ? window.i18n.t('ui.player') : 'Player';
                    $this.text(`${playerText} ${playerNumber[0]}`);
                }
            }
        });

        // 更新按钮文字
        updateDealButtonState();

        // 重新创建音频设置弹窗以更新语言
        $('#audio-settings-modal').remove();
        createAudioSettingsModal();
    }

    // 初始化语言切换
    setupLanguageSwitcher();

    // Global functions for unified settings modal
    window.toggleMusic = function() {
        musicMuted = !musicMuted;
        updateAudioVolumes();
        saveAudioSettings();
    };

    window.toggleEffects = function() {
        effectsMuted = !effectsMuted;
        updateAudioVolumes();
        saveAudioSettings();
    };

    window.isEffectsEnabled = function() {
        return !effectsMuted;
    };

    window.isMusicEnabled = function() {
        return !musicMuted;
    };

    window.setMusicVolume = function(volume) {
        musicVolume = volume;
        updateAudioVolumes();
        saveAudioSettings();
    };

    window.setEffectsVolume = function(volume) {
        effectsVolume = volume;
        updateAudioVolumes();
        saveAudioSettings();
    };

    window.isMusicMuted = function() {
        return musicMuted;
    };

    window.isEffectsMuted = function() {
        return effectsMuted;
    };

    window.getMusicVolume = function() {
        return musicVolume;
    };

    window.getEffectsVolume = function() {
        return effectsVolume;
    };

    window.testMusic = function() {
        if (backgroundMusic && !musicMuted) {
            backgroundMusic.currentTime = 0;
            backgroundMusic.play().catch(() => {});
            setTimeout(() => {
                backgroundMusic.pause();
            }, 3000);
        }
    };

    window.testEffects = function() {
        if (dealSound && !effectsMuted) {
            dealSound.currentTime = 0;
            dealSound.play().catch(() => {});
        }
    };

    window.toggleFullscreen = function() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(() => {});
        } else {
            document.exitFullscreen().catch(() => {});
        }
    };

    window.setDeckCount = function(deckCount) {
        const selectedDecks = parseInt(deckCount);
        gameSettings.deckCount = selectedDecks;
        // Apply the setting immediately if possible
        if (typeof resetGameForNewSettings === 'function') {
            resetGameForNewSettings();
        }
    };

    window.getCurrentDeckCount = function() {
        return gameSettings.deckCount;
    };

    // Auto-apply cached settings on page load
    if (typeof window.autoApplyGameSettings === 'function') {
        window.autoApplyGameSettings('blackjack');
    }

    // Share Modal Functions
    function showShareModal() {
        $('#share-modal').addClass('show').css('display', 'flex').hide().fadeIn(300);
        bindShareModalControls();
    }

    function hideShareModal() {
        $('#share-modal').fadeOut(300, function() {
            $(this).removeClass('show');
        });
    }

    function bindShareModalControls() {
        // Unbind previous events to prevent duplicates
        $('#share-facebook-direct, #share-twitter-direct, #share-whatsapp-direct, #share-telegram-direct').off('click');

        const currentUrl = window.location.href;
        const gameTitle = document.title || 'Blackjack Simulator';

        // Random share texts for variety (same as existing ones)
        const shareTexts = [
            `🎰 Master blackjack strategy with ${gameTitle}! Perfect your skills before hitting the real tables! 🃏♠️`,
            `🎯 Train like a pro! ${gameTitle} offers realistic blackjack simulation with multiple players! 🏆`,
            `♠️ Become a blackjack expert! ${gameTitle} simulates real casino conditions perfectly! 🎲`,
            `🃏 Practice makes perfect! ${gameTitle} helps you master card counting and strategy! 💎`,
            `🎲 Ready for Vegas? ${gameTitle} prepares you for real blackjack action! 🌟`,
            `💰 Free blackjack simulator that feels like the real casino! Try ${gameTitle} now! 🎰`,
            `🎯 Practice makes perfect! Sharpen your blackjack skills with ${gameTitle} - completely free! ♠️`,
            `🏆 Ready for 21? ${gameTitle} brings authentic casino blackjack to your browser! 🎲♥️`,
            `🌟 The most realistic blackjack training! ${gameTitle} simulates every casino detail! 🃏`,
            `💎 Professional blackjack training! ${gameTitle} teaches strategy and card counting! 🎰♠️`
        ];

        const shareText = shareTexts[Math.floor(Math.random() * shareTexts.length)];

        // Facebook share
        $('#share-facebook-direct').on('click', function() {
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`;
            window.open(facebookUrl, '_blank', 'width=600,height=400');
            hideShareModal();
        });

        // Twitter share
        $('#share-twitter-direct').on('click', function() {
            const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
            window.open(twitterUrl, '_blank', 'width=600,height=400');
            hideShareModal();
        });

        // WhatsApp share
        $('#share-whatsapp-direct').on('click', function() {
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + currentUrl)}`;
            window.open(whatsappUrl, '_blank');
            hideShareModal();
        });

        // Telegram share
        $('#share-telegram-direct').on('click', function() {
            const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
            window.open(telegramUrl, '_blank');
            hideShareModal();
        });
    }

    // Bind share button events
    $('#share-button').on('click', function() {
        showShareModal();
    });

    $('#share-modal-close').on('click', function() {
        hideShareModal();
    });

    $('#share-modal').on('click', function(e) {
        if (e.target === this) {
            hideShareModal();
        }
    });

});