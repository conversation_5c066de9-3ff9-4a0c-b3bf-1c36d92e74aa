/**
 * Mahjong Solitaire Game
 * International standard Mahjong Solitaire implementation
 */

class MahjongSolitaireGame {
    constructor() {
        this.tileSet = new MahjongTileSet();
        this.layout = new MahjongLayout();
        this.gameBoard = null;
        this.selectedTile = null;
        this.gameStarted = false;
        this.gameEnded = false;
        this.score = 0;
        this.moves = 0;
        this.startTime = null;
        
        // Game settings - will be updated based on screen size
        this.updateTileSettings();
    }

    /**
     * Update tile settings based on screen size and orientation
     */
    updateTileSettings() {
        // Check if mobile landscape with high DPI
        const isMobileLandscape = window.innerWidth <= 1024 &&
                                  window.innerWidth > window.innerHeight &&
                                  (window.devicePixelRatio >= 2 ||
                                   window.matchMedia('(min-resolution: 192dpi)').matches);

        if (isMobileLandscape) {
            // Mobile landscape - smaller tiles (32x42)
            this.tileWidth = 32;
            this.tileHeight = 42;
            this.tileSpacing = 2;
            this.layerOffset = 3;
        } else {
            // Desktop or portrait - normal tiles
            this.tileWidth = 40;
            this.tileHeight = 56;
            this.tileSpacing = 3;
            this.layerOffset = 4;
        }


    }

    /**
     * Initialize the game
     */
    init() {
        this.gameBoard = document.getElementById('mahjongGrid');
        if (!this.gameBoard) {
            console.error('Game board element not found');
            return;
        }



        this.setupEventListeners();
        this.newGame();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Restart button
        const restartBtn = document.getElementById('restartBtn');
        if (restartBtn) {
            restartBtn.addEventListener('click', () => this.newGame());
        }

        // Rules button
        const rulesBtn = document.getElementById('rulesBtn');
        if (rulesBtn) {
            rulesBtn.addEventListener('click', () => this.showRules());
        }

        // Fullscreen button
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        }
    }

    /**
     * Start a new game
     */
    newGame() {
        this.gameStarted = true;
        this.gameEnded = false;
        this.score = 0;
        this.moves = 0;
        this.selectedTile = null;
        this.startTime = Date.now();

        // Update tile settings for current orientation
        this.updateTileSettings();

        // Hide loading screen
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }

        // Show game container
        const gameContainer = document.getElementById('gameContainer');
        if (gameContainer) {
            gameContainer.style.display = 'flex';
        }

        // Clear game board
        this.gameBoard.innerHTML = '';

        // Generate and place tiles
        this.tileSet.generateTiles();
        this.tileSet.shuffle();
        this.layout.placeTiles(this.tileSet.tiles);

        // Render tiles
        this.renderTiles();
        this.updateUI();




    }

    /**
     * Render all tiles on the game board
     */
    renderTiles() {
        this.gameBoard.innerHTML = '';

        const activeTiles = this.tileSet.tiles.filter(tile => !tile.removed);
        if (activeTiles.length === 0) return;

        let minRow = Math.min(...activeTiles.map(tile => tile.row));
        let maxRow = Math.max(...activeTiles.map(tile => tile.row));
        let minCol = Math.min(...activeTiles.map(tile => tile.col));
        let maxCol = Math.max(...activeTiles.map(tile => tile.col));

        const layoutWidth = (maxCol - minCol + 1) * (this.tileWidth + this.tileSpacing) - this.tileSpacing;
        const layoutHeight = (maxRow - minRow + 1) * (this.tileHeight + this.tileSpacing) - this.tileSpacing;

        const boardRect = this.gameBoard.getBoundingClientRect();
        const boardWidth = boardRect.width;
        const boardHeight = boardRect.height;

        const baseOffsetX = Math.max(0, (boardWidth - layoutWidth) / 2);
        const baseOffsetY = Math.max(0, (boardHeight - layoutHeight) / 2);

        this.tileSet.tiles.forEach(tile => {
            if (!tile.removed) {
                const relativeCol = tile.col - minCol;
                const relativeRow = tile.row - minRow;

                const x = baseOffsetX + relativeCol * (this.tileWidth + this.tileSpacing);
                const y = baseOffsetY + relativeRow * (this.tileHeight + this.tileSpacing);

                const layerX = x - (tile.layer * this.layerOffset);
                const layerY = y - (tile.layer * this.layerOffset);

                tile.setPosition(layerX, layerY);
                const element = tile.createElement();

                element.style.zIndex = tile.layer * 10 + 1;
                element.addEventListener('click', (e) => this.handleTileClick(tile, e));

                this.gameBoard.appendChild(element);
            }
        });


    }

    /**
     * Handle tile click
     */
    handleTileClick(tile, event) {
        event.preventDefault();

        if (!this.gameStarted || this.gameEnded || tile.removed || tile.blocked) {
            return;
        }

        if (this.selectedTile === tile) {
            this.deselectTile();
            return;
        }

        if (this.selectedTile === null) {
            this.selectTile(tile);
        } else {
            if (this.selectedTile.canMatch(tile)) {
                this.matchTiles(this.selectedTile, tile);
            } else {
                this.deselectTile();
                this.selectTile(tile);
            }
        }
    }

    /**
     * Select a tile
     */
    selectTile(tile) {
        this.selectedTile = tile;
        tile.setSelected(true);
    }

    /**
     * Deselect current tile
     */
    deselectTile() {
        if (this.selectedTile) {
            this.selectedTile.setSelected(false);
            this.selectedTile = null;
        }
    }

    /**
     * Match two tiles
     */
    matchTiles(tile1, tile2) {
        // Remove tiles
        this.tileSet.removeTile(tile1);
        this.tileSet.removeTile(tile2);
        
        // Remove from layout
        this.layout.removeTile(tile1);
        this.layout.removeTile(tile2);

        // Update game state
        this.moves++;
        this.score += 10;
        this.selectedTile = null;

        // Update blocked status
        this.layout.updateBlockedStatus(this.tileSet.getActiveTiles());

        // Update tile appearances
        this.updateTileAppearances();

        // Check win/lose conditions
        setTimeout(() => {
            if (this.tileSet.isComplete()) {
                this.gameWon();
            } else if (!this.hasValidMoves()) {
                this.gameOver();
            }
        }, 300);

        this.updateUI();
    }

    /**
     * Update tile appearances based on blocked status
     */
    updateTileAppearances() {
        this.tileSet.getActiveTiles().forEach(tile => {
            if (tile.element) {
                tile.element.className = tile.getCSSClasses();
            }
        });


    }

    /**
     * Check if there are valid moves available
     */
    hasValidMoves() {
        const freeTiles = this.tileSet.getActiveTiles().filter(tile => !tile.blocked);
        
        for (let i = 0; i < freeTiles.length; i++) {
            for (let j = i + 1; j < freeTiles.length; j++) {
                if (freeTiles[i].canMatch(freeTiles[j])) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Show rules modal
     */
    showRules() {
        const rulesModal = document.getElementById('rulesModal');
        if (rulesModal) {
            rulesModal.style.display = 'flex';
        }
    }

    /**
     * Toggle fullscreen mode
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Error attempting to enable fullscreen:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }



    /**
     * Show custom message modal using game's built-in modal
     */
    showMessage(title, content, buttons = [{ text: 'OK', type: 'primary' }], messageType = 'info') {
        const messageModal = document.getElementById('gameMessage');
        const messageTitle = document.getElementById('messageTitle');
        const messageText = document.getElementById('messageText');
        const messageButtons = document.querySelector('.message-buttons');
        const messageContent = document.querySelector('.message-content');

        if (messageModal && messageTitle && messageText && messageButtons && messageContent) {
            // Set title and content
            messageTitle.textContent = title;
            messageText.textContent = content;

            // Add message type class for styling
            messageContent.className = `message-content message-${messageType}`;

            // Add icon based on message type
            let icon = '';
            switch(messageType) {
                case 'success':
                    icon = '🎉 ';
                    break;
                case 'error':
                    icon = '😔 ';
                    break;
                case 'warning':
                    icon = '⚠️ ';
                    break;
                default:
                    icon = '';
            }
            messageTitle.textContent = icon + title;

            // Clear existing buttons
            messageButtons.innerHTML = '';

            // Add new buttons
            buttons.forEach(button => {
                const btn = document.createElement('button');
                btn.className = `btn btn-${button.type || 'primary'}`;
                btn.textContent = button.text;
                btn.addEventListener('click', () => {
                    messageModal.style.display = 'none';
                    // Reset message content class
                    messageContent.className = 'message-content';
                    if (button.callback) {
                        button.callback();
                    }
                });
                messageButtons.appendChild(btn);
            });

            // Show modal
            messageModal.style.display = 'flex';
        } else {
            // Fallback to alert if modal elements not found
            alert(title + '\n\n' + content);
        }
    }

    /**
     * Show message modal with share functionality
     */
    showMessageWithShare(title, content, buttons = [{ text: 'OK', type: 'primary' }], messageType = 'info') {
        const messageModal = document.getElementById('gameMessage');
        const messageTitle = document.getElementById('messageTitle');
        const messageText = document.getElementById('messageText');
        const messageButtons = document.querySelector('.message-buttons');
        const messageContent = document.querySelector('.message-content');

        if (messageModal && messageTitle && messageText && messageButtons && messageContent) {
            // Set title and content
            messageTitle.textContent = title;
            messageText.textContent = content;

            // Add message type class for styling
            messageContent.className = `message-content message-${messageType}`;

            // Add icon based on message type
            let icon = '';
            switch(messageType) {
                case 'success':
                    icon = '🎉 ';
                    break;
                case 'error':
                    icon = '😔 ';
                    break;
                case 'warning':
                    icon = '⚠️ ';
                    break;
                default:
                    icon = '';
            }
            messageTitle.textContent = icon + title;

            // Clear existing buttons
            messageButtons.innerHTML = '';

            // Remove any existing share sections to prevent duplicates
            const existingShareSections = messageContent.querySelectorAll('.share-section');
            existingShareSections.forEach(section => section.remove());

            // Add share section
            const shareSection = document.createElement('div');
            shareSection.className = 'share-section';
            shareSection.innerHTML = `
                <div class="share-text">
                    <p>Share this amazing game with friends:</p>
                </div>
                <div class="share-buttons">
                    <button id="share-facebook-mahjong" class="share-button facebook" title="Share on Facebook">
                        <img src="/mahjong-solitaire/images/facebook.svg" alt="Facebook" />
                    </button>
                    <button id="share-twitter-mahjong" class="share-button twitter" title="Share on Twitter">
                        <img src="/mahjong-solitaire/images/twitter.svg" alt="Twitter" />
                    </button>
                    <button id="share-whatsapp-mahjong" class="share-button whatsapp" title="Share on WhatsApp">
                        <img src="/mahjong-solitaire/images/whatsapp.svg" alt="WhatsApp" />
                    </button>
                    <button id="share-telegram-mahjong" class="share-button telegram" title="Share on Telegram">
                        <img src="/mahjong-solitaire/images/telegram.svg" alt="Telegram" />
                    </button>
                </div>
            `;

            // Insert share section before buttons
            messageContent.insertBefore(shareSection, messageButtons);

            // Add new buttons
            buttons.forEach(button => {
                const btn = document.createElement('button');
                btn.className = `btn btn-${button.type || 'primary'}`;
                btn.textContent = button.text;
                btn.addEventListener('click', () => {
                    messageModal.style.display = 'none';
                    // Reset message content class and remove share section
                    messageContent.className = 'message-content';
                    const existingShareSection = messageContent.querySelector('.share-section');
                    if (existingShareSection) {
                        existingShareSection.remove();
                    }
                    if (button.callback) {
                        button.callback();
                    }
                });
                messageButtons.appendChild(btn);
            });

            // Bind share functionality
            this.bindMahjongShareControls();

            // Show modal
            messageModal.style.display = 'flex';
        } else {
            // Fallback to alert if modal elements not found
            alert(title + '\n\n' + content);
        }
    }

    /**
     * Bind share controls for Mahjong Solitaire
     */
    bindMahjongShareControls() {
        const currentUrl = window.location.href;
        const gameTitle = document.title || 'Mahjong Solitaire';

        // Random share texts for variety
        const shareTexts = [
            `🀄 Master the ancient art of Mahjong Solitaire! ${gameTitle} brings traditional tile-matching to your browser! 🧩✨`,
            `🧠 Train your mind with ${gameTitle} - the most relaxing puzzle game online! Perfect for stress relief! 🌸`,
            `🎯 Challenge your pattern recognition skills! ${gameTitle} offers endless brain training fun! 🀄🎲`,
            `🌟 Experience the zen of tile matching! ${gameTitle} combines strategy with meditation! 🧘‍♀️🀄`,
            `🏆 From beginner to master - ${gameTitle} helps you perfect your Mahjong Solitaire skills! 🎯`,
            `🎲 Traditional Chinese tiles meet modern gameplay! Discover ${gameTitle} today! 🀄💎`,
            `🧩 Sharpen your focus with ${gameTitle} - the ultimate tile-matching challenge! 🌟`,
            `💰 Free Mahjong Solitaire with beautiful graphics! Try ${gameTitle} risk-free! 🀄`,
            `🎯 Clear the board, clear your mind! ${gameTitle} offers the perfect mental escape! 🧠`,
            `🌸 Authentic Mahjong tiles in a modern interface! ${gameTitle} preserves tradition! 🀄🏆`
        ];

        const shareText = shareTexts[Math.floor(Math.random() * shareTexts.length)];

        // Remove existing event listeners by cloning and replacing elements
        const facebookBtn = document.getElementById('share-facebook-mahjong');
        const twitterBtn = document.getElementById('share-twitter-mahjong');
        const whatsappBtn = document.getElementById('share-whatsapp-mahjong');
        const telegramBtn = document.getElementById('share-telegram-mahjong');

        // Helper function to clone element and remove all event listeners
        const cloneAndReplace = (element) => {
            if (element) {
                const newElement = element.cloneNode(true);
                element.parentNode.replaceChild(newElement, element);
                return newElement;
            }
            return null;
        };

        const newFacebookBtn = cloneAndReplace(facebookBtn);
        const newTwitterBtn = cloneAndReplace(twitterBtn);
        const newWhatsappBtn = cloneAndReplace(whatsappBtn);
        const newTelegramBtn = cloneAndReplace(telegramBtn);

        if (newFacebookBtn) {
            newFacebookBtn.addEventListener('click', () => {
                const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`;
                window.open(facebookUrl, '_blank', 'width=600,height=400');
            });
        }

        if (newTwitterBtn) {
            newTwitterBtn.addEventListener('click', () => {
                const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
                window.open(twitterUrl, '_blank', 'width=600,height=400');
            });
        }

        if (newWhatsappBtn) {
            newWhatsappBtn.addEventListener('click', () => {
                const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + currentUrl)}`;
                window.open(whatsappUrl, '_blank');
            });
        }

        if (newTelegramBtn) {
            newTelegramBtn.addEventListener('click', () => {
                const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
                window.open(telegramUrl, '_blank');
            });
        }
    }

    /**
     * Shuffle remaining tiles
     */
    shuffleRemainingTiles() {
        const activeTiles = this.tileSet.getActiveTiles();
        const positions = activeTiles.map(tile => ({
            row: tile.row,
            col: tile.col,
            layer: tile.layer
        }));

        // Shuffle positions
        for (let i = positions.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [positions[i], positions[j]] = [positions[j], positions[i]];
        }

        // Reassign positions
        activeTiles.forEach((tile, index) => {
            const pos = positions[index];
            tile.row = pos.row;
            tile.col = pos.col;
            tile.layer = pos.layer;
        });

        this.layout.updateBlockedStatus(activeTiles);
        this.renderTiles();
    }

    /**
     * Game won
     */
    gameWon() {
        this.gameEnded = true;
        const timeElapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(timeElapsed / 60);
        const seconds = timeElapsed % 60;
        const timeStr = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;

        this.showMessageWithShare('Congratulations!',
            `You completed the game!\n\nTime: ${timeStr}\nMoves: ${this.moves}\nScore: ${this.score}`,
            [
                { text: 'New Game', type: 'primary', callback: () => this.newGame() },
                { text: 'Home', type: 'secondary', callback: () => window.location.href = '/' }
            ],
            'success'
        );
    }

    /**
     * Game over
     */
    gameOver() {
        this.gameEnded = true;
        this.showMessage('Game Over',
            'No more valid moves available. Try a new game!',
            [
                { text: 'New Game', type: 'primary', callback: () => this.newGame() },
                { text: 'Home', type: 'secondary', callback: () => window.location.href = '/' }
            ],
            'error'
        );
    }

    /**
     * Update UI elements
     */
    updateUI() {
        const scoreElement = document.getElementById('scoreDisplay');
        const tilesElement = document.getElementById('tilesLeft');

        if (scoreElement) scoreElement.textContent = `Score: ${this.score}`;
        if (tilesElement) {
            const remaining = this.tileSet.getActiveTiles().length;
            tilesElement.textContent = `Tiles: ${remaining}`;
        }
    }
}



// Check orientation and show/hide landscape prompt
function checkOrientation() {
    const isMobile = window.innerWidth <= 1024;
    const isPortrait = window.innerHeight > window.innerWidth;
    const landscapePrompt = document.getElementById('landscape-prompt');
    const gameContainer = document.querySelector('.game-container');

    if (isMobile && isPortrait) {
        // 移动端竖屏：显示横屏提示
        if (landscapePrompt) landscapePrompt.style.display = 'flex';
        if (gameContainer) gameContainer.style.display = 'none';
    } else if (isMobile && !isPortrait) {
        // 移动端横屏：显示游戏内容
        if (landscapePrompt) landscapePrompt.style.display = 'none';
        if (gameContainer) gameContainer.style.display = 'flex';

        // Update tile settings and re-render if game exists
        if (window.game && window.game.gameStarted) {
            window.game.updateTileSettings();
            window.game.renderTiles();
        }
    } else {
        // 桌面端：始终显示游戏内容
        if (landscapePrompt) landscapePrompt.style.display = 'none';
        if (gameContainer) gameContainer.style.display = 'flex';

        // Update tile settings and re-render if game exists
        if (window.game && window.game.gameStarted) {
            window.game.updateTileSettings();
            window.game.renderTiles();
        }
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.game = new MahjongSolitaireGame();
    window.game.init();

    // Check orientation on load
    checkOrientation();

    // Listen for orientation and resize changes
    window.addEventListener('orientationchange', () => {
        setTimeout(checkOrientation, 100);
    });
    window.addEventListener('resize', checkOrientation);
});
