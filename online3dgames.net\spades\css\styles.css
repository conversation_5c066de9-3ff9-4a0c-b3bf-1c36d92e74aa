* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.landscape-prompt {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a4d2e;
    z-index: 10000;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
}

.landscape-content {
    max-width: 400px;
}

.rotate-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: rotate 2s infinite ease-in-out;
}

@keyframes rotate {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(90deg); }
}

.landscape-content h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.landscape-content p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a4d2e;
    min-height: 100vh;
    color: #fff;
    overflow-x: auto;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.game-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    min-height: 100dvh;
    position: relative;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 20px;
}

.header-left h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.game-info {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.round-info, .trick-info {
    background: rgba(255,255,255,0.1);
    padding: 5px 15px;
    border-radius: 20px;
}

.info-label {
    font-weight: bold;
    margin-right: 5px;
}

.header-right {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn {
    padding: 5px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(45deg, #1e3a8a, #1d4ed8);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #1d4ed8, #2563eb);
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

#fullscreenBtn {
    font-size: 18px;
    padding: 8px 12px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#fullscreenBtn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

:fullscreen {
    background: #1a4d2e;
}

:-webkit-full-screen {
    background: #1a4d2e;
}

:-moz-full-screen {
    background: #1a4d2e;
}

:-ms-fullscreen {
    background: #1a4d2e;
}

.game-board {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-height: 600px;
}

.player-score {
    font-size: 16px;
    font-weight: bold;
    color: #FFD700;
    background: rgba(0,0,0,0.4);
    padding: 2px 8px;
    border-radius: 12px;
    margin: 0 5px;
    border: 1px solid rgba(255,215,0,0.3);
    text-shadow: 0 1px 2px rgba(0,0,0,0.8);
}

.play-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.player-area {
    display: flex;
    align-items: center;
    gap: 15px;
}

.player-area.north {
    flex-direction: column;
    justify-content: center;
    margin-bottom: 15px;
}

.player-area.south {
    justify-content: center;
    flex-direction: column-reverse;
    margin-top: 15px;
    height: 280px;
}

.center-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    min-height: 320px;
    padding: 0 20px;
}

.player-area.west {
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
}

.player-area.east {
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
}

.player-hand {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100px;
    overflow: visible;
}

.player-area.south .player-hand {
    min-height: 80px;
}

.player-area.west .player-hand,
.player-area.east .player-hand {
    min-width: 120px;
    min-height: 220px;
    flex-direction: column;
    overflow: visible;
}

.player-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.1);
    padding: 8px 12px;
    border-radius: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.player-info .player-name {
    font-weight: bold;
    font-size: 14px;
}

.player-info .cards-count {
    font-size: 12px;
    opacity: 0.8;
    background: rgba(255,255,255,0.2);
    padding: 2px 6px;
    border-radius: 8px;
}

.trick-area {
    position: relative;
    width: 300px;
    height: 300px;
    background: rgba(0,0,0,0.2);
    border-radius: 50%;
    border: 2px dashed rgba(255,255,255,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.passing-slots {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 15px;
    background: rgba(0,0,0,0.6);
    border-radius: 15px;
    padding: 20px;
    z-index: 10;
}

.passing-slot {
    width: 80px;
    height: 112px;
    border: 3px dashed rgba(255,215,0,0.6);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: rgba(255,215,0,0.1);
    transition: all 0.3s ease;
}

.passing-slot:hover {
    border-color: rgba(255,215,0,0.9);
    background: rgba(255,215,0,0.2);
}

.passing-slot.filled {
    border-color: #FFD700;
    background: rgba(255,215,0,0.3);
}

.passing-button-container {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
}

.passing-button-container .btn {
    padding: 10px 20px;
    font-size: 14px;
    background: #1e3a8a;
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.passing-button-container .btn:hover {
    background: #45a049;
    transform: scale(1.05);
}

.passing-button-container .btn:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
}

.played-card {
    position: absolute;
    width: 75px;
    height: 105px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.played-card.north-card {
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
}

.played-card.south-card {
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
}

.played-card.west-card {
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
}

.played-card.east-card {
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

.trick-winner-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.trick-winner-indicator.show {
    opacity: 1;
}

.card {
    width: 75px;
    height: 105px;
    background: white;
    border-radius: 8px;
    border: 1px solid #ccc;
    position: absolute;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 5px;
    font-size: 18px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 3px 6px rgba(0,0,0,0.3);
    transform-origin: center bottom;
}

.player-area.south .card {
    transform-origin: center bottom;
}

.player-area.north .card {
    transform-origin: center top;
}

.player-area.west .card {
    transform-origin: center center;
}

.player-area.east .card {
    transform-origin: center center;
}

.card.face-down {
    background: linear-gradient(45deg, #8B0000, #DC143C);
    color: transparent;
}

.card.face-down::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.3) 2px,
        rgba(255,255,255,0.3) 4px
    );
    border-radius: 3px;
}

.card.red {
    color: #dc143c;
}

.card.black {
    color: #000;
}

.card-top, .card-bottom {
    display: flex;
    align-items: center;
    gap: 1px;
    font-size: 18px;
}

.card-bottom {
    transform: rotate(180deg);
    align-self: flex-end;
}

.card-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
}

.card.selectable {
    cursor: pointer;
}

.card.unplayable {
    cursor: default;
    pointer-events: none;
}

.card.unplayable::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(128, 128, 128, 0.6);
    border-radius: 8px;
    pointer-events: none;
    z-index: 1;
    transition: opacity 0.3s ease;
}

.card.playable {
    opacity: 1;
    filter: none;
    cursor: pointer;
}



.card.selectable:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}





.card.flying {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
}

.card.flying-to-center {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 12px 30px rgba(0,0,0,0.6);
    transform: scale(1.05) rotate(0deg) !important;
}

.card.flying-to-player {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 6px 20px rgba(0,0,0,0.5);
    transform: scale(0.8) !important;
}

.game-status {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    text-align: center;
    padding: 5px 25px;
    background: rgba(0,0,0,0.7);
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    max-width: 80%;
    font-size: 16px;
}

.status-message {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.passing-info {
    font-size: 14px;
    color: rgba(255,255,255,0.8);
    margin-top: 5px;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.3);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    z-index: 9999;
    backdrop-filter: blur(5px);
    padding-top: 20px;
}

#passingModal {
    background: rgba(0,0,0,0.1);
    pointer-events: none;
}

#passingModal .modal-content {
    pointer-events: all;
}

.modal.hidden {
    display: none;
}

.modal-content {
    padding: 20px;
    border-radius: 15px;
    max-width: 400px;
    width: 80%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    border: 2px solid rgba(255,255,255,0.2);
}

#passingModal .modal-content {
    max-width: 350px;
    padding: 15px;
    margin-top: 0;
}

.modal-content h2, .modal-content h3 {
    margin-bottom: 5px;
    color: #fff;
    font-size: 18px;
    padding: 5px 0;
}

.passing-direction {
    font-size: 16px;
    margin-bottom: 20px;
    padding: 10px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
}

.selected-cards {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin: 15px 0;
    min-height: 60px;
    align-items: center;
    background: rgba(0,0,0,0.2);
    border-radius: 8px;
    padding: 10px;
    border: 2px dashed rgba(255,255,255,0.3);
}

.selected-cards .card {
    width: 40px;
    height: 56px;
    font-size: 8px;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 10px;
}

.final-scores {
    margin: 20px 0;
}

.final-scores .score-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.final-scores .score-row:last-child {
    border-bottom: none;
}

.final-scores .winner {
    background: rgba(255,215,0,0.2);
    border-radius: 5px;
    padding: 5px;
    font-weight: bold;
}

.scoreboard-modal {
    max-width: 700px;
    width: 95%;
    max-height: 90%;
    overflow-y: auto;
}

.scoreboard-container {
    background: rgba(0,0,0,0.3);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    border: 2px solid rgba(255,255,255,0.2);
    max-height: 600px;
    overflow-y: auto;
}

.game-summary {
    background: rgba(30,58,138,0.15);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid rgba(59,130,246,0.3);
    text-align: center;
}

.game-summary h3 {
    color: #E8F4FD;
    margin: 0 0 10px 0;
    font-size: 18px;
}

.summary-stats{
    display: flex;
    justify-content: space-around;
}

.game-summary .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.summary-stat {
    background: rgba(0,0,0,0.2);
    padding: 8px;
    border-radius: 5px;
    text-align: center;
}

.summary-stat .stat-label {
    font-size: 12px;
    color: rgba(255,255,255,0.7);
    margin-bottom: 2px;
}

.summary-stat .stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #B8D4F0;
}

.game-resume-modal {
    max-width: 650px;
    width: 90%;
    max-height: 95%;
    overflow-y: auto;
    padding: 5px;
}

.game-resume-container {
    background: rgba(0,0,0,0.3);
    border-radius: 10px;
    padding: 10px;
    margin: 5px 0;
    border: 2px solid rgba(255,255,255,0.2);
}

.resume-summary {
    background: rgba(59,130,246,0.3);
    border-radius: 8px;
    padding: 5px;
    margin-bottom: 10px;
    border: 1px solid rgba(59,130,246,0.3);
    text-align: center;
}

.resume-summary h3 {
    color: #E8F4FD;
    margin: 0 0 10px 0;
    font-size: 18px;
}

.resume-scoreboard {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 8px;
}

.final-rankings-table {
    background: rgba(0,0,0,0.3);
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
    border: 2px solid rgba(59,130,246,0.3);
}

.ranking-row {
    display: grid;
    grid-template-columns: 80px 1fr 80px;
    gap: 15px;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 8px;
    align-items: center;
    transition: all 0.3s ease;
}

.ranking-header {
    background: rgba(59,130,246,0.2);
    font-weight: bold;
    color: #E8F4FD;
    margin-bottom: 15px;
    border: 1px solid rgba(59,130,246,0.4);
}

.ranking-row:not(.ranking-header) {
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(255,255,255,0.1);
}

.winner-row {
    background: linear-gradient(45deg, rgba(255,215,0,0.15), rgba(255,223,0,0.1)) !important;
    border: 2px solid rgba(255,215,0,0.4) !important;
    box-shadow: 0 0 15px rgba(255,215,0,0.2);
}

.ranking-place {
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    color: #B8D4F0;
}

.winner-place {
    color: #FFD700 !important;
    font-size: 18px;
    text-shadow: 0 0 10px rgba(255,215,0,0.5);
}

.ranking-name {
    text-align: center;
    font-size: 16px;
    color: #FFFFFF;
}

.winner-name {
    color: #FFD700 !important;
    font-weight: bold;
    font-size: 18px;
    text-shadow: 0 0 10px rgba(255,215,0,0.5);
}

.ranking-score {
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    color: #B8D4F0;
}

.winner-score {
    color: #FFD700 !important;
    font-size: 18px;
    text-shadow: 0 0 10px rgba(255,215,0,0.5);
}

.scoreboard-body {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.scoreboard-row {
    display: grid;
    grid-template-columns: 80px repeat(4, 1fr);
    gap: 2px;
    min-height: 45px;
}

.scoreboard-row.header-row {
    background: rgba(255,255,255,0.1);
    border-radius: 8px 8px 0 0;
    font-weight: bold;
    color: #E8F4FD;
    text-shadow: 0 1px 2px rgba(0,0,0,0.8);
}

.round-cell, .player-cell {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 4px;
    background: rgba(0,0,0,0.4);
    border: 1px solid rgba(255,255,255,0.1);
    text-align: center;
    font-size: 14px;
}

.header-row .round-cell, .header-row .player-cell {
    background: rgba(59, 130, 246,0.2);
    border: 1px solid rgba(59, 130, 246,0.3);
    font-size: 16px;
}

.round-cell {
    font-weight: bold;
    color: #B8D4F0;
}

.player-cell .round-score {
    font-size: 16px;
    font-weight: bold;
    color: #FF6B6B;
    margin-bottom: 2px;
}

.player-cell .total-score {
    font-size: 12px;
    color: #B8D4F0;
    opacity: 0.8;
}

.scoreboard-row:nth-child(even) .round-cell,
.scoreboard-row:nth-child(even) .player-cell {
    background: rgba(0,0,0,0.2);
}

.scoreboard-row:hover .round-cell,
.scoreboard-row:hover .player-cell {
    background: rgba(255,255,255,0.1);
}
.help-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.help-panel.hidden {
    display: none;
}

.help-content {
    background: #2d5016;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    border: 2px solid rgba(255,255,255,0.2);
}

.help-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.help-header h3 {
    margin: 0;
    color: #fff;
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.help-body {
    padding: 20px 30px;
}

.help-section {
    margin-bottom: 25px;
}

.help-section h4 {
    color: #fff;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.help-section p {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    margin-bottom: 10px;
}

.help-section ul {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    padding-left: 20px;
}

.help-section li {
    margin-bottom: 8px;
}

.help-footer {
    padding: 20px 30px;
    border-top: 1px solid rgba(255,255,255,0.2);
    text-align: center;
}

@media (max-width: 1024px) {
    .game-header {
        margin-bottom: 5px;
        z-index: 1000;
        position: relative;
    }

    .header-left h1 {
        font-size: 1rem;
    }

    .scores-panel {
        gap: 20px;
        padding: 10px;
    }

    .center-area {
        min-height: unset;
        max-height: 230px;
        padding: 0 15px;
    }

    .player-area.north {
        margin-bottom: 0;
        gap: 5;
    }

    .player-area{
        position: relative;
    }

    .game-status{
        display: none;
    }

    .player-area.south {
        margin-top: 10px;
        height: auto;
    }

    .player-area.south .player-info{
        position: absolute;
        left: 75%;
        top: 1px;
    }

    .player-area.north .player-info{
        position: absolute;
        right: 60%;
        top: 65%;
    }

    .card {
        width: 60px;
        height: 84px;
        font-size: 15px;
    }

    .card-center {
        font-size: 20px;
    }

    .played-card {
        width: 60px;
        height: 84px;
    }

    .trick-area {
        width: 250px;
        height: 250px;
    }

    .passing-slot {
        width: 65px;
        height: 91px;
    }

    .passing-slots {
        padding: 15px;
        gap: 10px;
    }

    .game-board{
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
    }

    .player-area.west .player-hand, .player-area.east .player-hand{
        min-height: 140px;
    }
}

@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }

    .game-header {
        flex-direction: column;
        gap: 15px;
        margin-bottom: 10px;
    }

    .header-left {
        text-align: center;
    }

    .header-left h1 {
        font-size: 1.8rem;
        margin-bottom: 5px;
    }

    .game-info {
        justify-content: center;
        gap: 15px;
    }

    .scores-panel {
        gap: 15px;
        padding: 8px;
        flex-wrap: wrap;
    }

    .player-score {
        min-width: 60px;
    }

    .player-score .score-value {
        font-size: 20px;
    }

    .center-area {
        min-height: 220px;
        padding: 0 10px;
    }

    .player-area.north {
        margin-bottom: 8px;
    }

    .player-area.south {
        margin-top: 8px;
    }

    .trick-area {
        width: 120px;
        height: 120px;
    }

    .card {
        width: 50px;
        height: 70px;
        font-size: 12px;
        padding: 3px;
    }

    .card-center {
        font-size: 16px;
    }

    .card-top, .card-bottom {
        font-size: 12px;
    }

    .passing-slot {
        width: 55px;
        height: 77px;
    }

    .passing-slots {
        padding: 12px;
        gap: 8px;
    }

    .played-card {
        width: 40px;
        height: 56px;
    }

    .player-area.south .player-hand {
        min-height: 90px;
    }

    .player-area.north .player-hand {
        min-height: 80px;
        margin-top: 8px;
    }

    .player-area.west .player-hand,
    .player-area.east .player-hand {
        min-width: 80px;
        min-height: 140px;
    }

    .modal-content {
        padding: 20px;
        width: 95%;
    }

    .selected-cards {
        flex-wrap: wrap;
        gap: 5px;
    }

    .help-content {
        width: 95%;
        max-height: 85vh;
    }

    .help-header, .help-body, .help-footer {
        padding: 15px 20px;
    }

    .scoreboard-modal {
        width: 95%;
        max-width: none;
    }

    .scoreboard-container {
        padding: 15px;
    }

    .scoreboard-row {
        grid-template-columns: 60px repeat(4, 1fr);
        gap: 1px;
        min-height: 40px;
    }

    .round-cell, .player-cell {
        padding: 6px 2px;
        font-size: 12px;
    }

    .header-row .round-cell, .header-row .player-cell {
        font-size: 14px;
    }

    .player-cell .round-score {
        font-size: 14px;
    }

    .player-cell .total-score {
        font-size: 10px;
    }



    .scoreboard-modal {
        width: 98%;
        max-height: 85%;
    }

    .game-summary .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .summary-stat {
        padding: 6px;
    }

    .summary-stat .stat-value {
        font-size: 14px;
    }

    .game-resume-modal {
        width: 95%;
    }

    .resume-scoreboard {
        max-height: 250px;
    }

    .final-rankings-table {
        padding: 10px;
        margin-top: 10px;
    }

    .ranking-row {
        grid-template-columns: 60px 1fr 60px;
        gap: 10px;
        padding: 10px 8px;
        margin-bottom: 6px;
    }

    .ranking-place,
    .ranking-name,
    .ranking-score {
        font-size: 14px;
    }

    .winner-place,
    .winner-name,
    .winner-score {
        font-size: 16px;
    }

    .game-status {
        top: 10px;
        padding: 12px 20px;
        max-width: 90%;
    }

    .status-message {
        font-size: 16px;
    }

    .game-board {
        padding-top: 60px;
    }

    #fullscreenBtn {
        font-size: 16px;
        padding: 10px 16px;
        min-width: 48px;
    }
}

@media screen and (max-width: 768px) and (orientation: portrait) {
    .landscape-prompt {
        display: flex !important;
    }

    .game-container {
        display: none !important;
    }
}

/* 移动端竖屏时默认显示横屏提示，避免扑克牌闪现 */
@media screen and (max-width: 768px) {
    .landscape-prompt {
        display: flex;
    }

    .game-container {
        display: none;
    }
}

/* 移动端横屏时显示游戏内容 */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .landscape-prompt {
        display: none !important;
    }

    .game-container {
        display: flex !important;
    }
}

@media (max-width: 480px) {
    .header-left h1 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 8px 16px;
        font-size: 12px;
    }

    .card {
        width: 45px;
        height: 63px;
        font-size: 11px;
        padding: 2px;
    }

    .card-center {
        font-size: 14px;
    }

    .played-card {
        width: 45px;
        height: 63px;
    }

    .trick-area {
        width: 225px;
        height: 225px;
    }

    .passing-slot {
        width: 50px;
        height: 70px;
    }

    .passing-slots {
        padding: 10px;
        gap: 6px;
    }

    .slot-number {
        width: 16px;
        height: 16px;
        font-size: 10px;
    }

    .player-hand {
        gap: 2px;
    }

    .scores-panel {
        gap: 10px;
        padding: 5px;
    }

    .player-score .score-value {
        font-size: 18px;
    }



    .scoreboard-row {
        grid-template-columns: 50px repeat(4, 1fr);
        min-height: 35px;
    }

    .round-cell, .player-cell {
        padding: 4px 1px;
        font-size: 11px;
    }

    .header-row .round-cell, .header-row .player-cell {
        font-size: 12px;
    }

    .player-cell .round-score {
        font-size: 12px;
    }

    .player-cell .total-score {
        font-size: 9px;
    }

    #fullscreenBtn {
        font-size: 14px;
        padding: 6px 10px;
        min-width: 36px;
    }
}

.card.dragging {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6);
    transform: scale(1.1);
    transition: none !important;
}

.flying-card {
    z-index: 9999 !important;
    pointer-events: none;
    box-shadow: 0 8px 16px rgba(0,0,0,0.5);
    will-change: transform, left, top;
    backface-visibility: hidden;
    transform-style: preserve-3d;
}

.card.drag-placeholder {
    opacity: 0.3;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px dashed rgba(255, 255, 255, 0.5) !important;
}

.card:active {
    transform: scale(0.95);
}

.player-area.south .card.selectable:hover {
    transform: translateY(-8px);
}


/* Mobile landscape optimization - comprehensive layout adjustment */
@media (max-width: 768px) and (orientation: landscape) {
    /* Game container overall optimization */
    .game-container {
        padding: 5px;
        height: 100vh;
        overflow: hidden;
    }

    /* Game header compression */
    .game-header {
        margin-bottom: 3px;
        padding: 8px 15px;
    }

    .header-left h1 {
        font-size: 1.2rem;
        margin-bottom: 2px;
    }

    .game-info {
        gap: 10px;
    }

    .info-label {
        font-size: 12px;
    }

    /* Game board layout optimization */
    .game-board {
        min-height: auto;
        height: calc(100vh - 60px);
        gap: 5px;
        padding-top: 0;
    }

    /* Game area height compression */
    .play-area {
        height: 100%;
        max-height: calc(100vh - 60px);
    }

    /* Central area significant compression */
    .center-area {
        min-height: 120px;
        max-height: 140px;
        padding: 0 8px;
        flex: 0 0 auto;
    }

    /* Player area spacing minimization */
    .player-area.north {
        margin-bottom: 2px;
    }

    .player-area.south {
        margin-top: 2px;
    }

    .player-area.west, .player-area.east {
        margin: 0 3px;
    }

    /* Hand area height compression */
    .player-area.south .player-hand {
        min-height: 60px;
        max-height: 70px;
    }

    .player-area.north .player-hand {
        min-height: 50px;
        max-height: 60px;
    }

    .player-area.west .player-hand,
    .player-area.east .player-hand {
        min-width: 60px;
        max-width: 80px;
        min-height: 100px;
        max-height: 120px;
    }

    /* Card size optimization - maintain operability */
    .card {
        width: 40px !important;
        height: 56px !important;
        font-size: 9px !important;
        padding: 2px !important;
        border-radius: 4px;
        min-width: 40px;
        min-height: 56px;
    }

    /* Enhanced touch area */
    .card.selectable {
        position: relative;
    }

    .card.selectable::before {
        content: '';
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        z-index: -1;
        pointer-events: auto;
    }

    .card-center {
        font-size: 12px !important;
    }

    .card-top, .card-bottom {
        font-size: 9px !important;
        gap: 0px;
    }

    /* Played area cards */
    .played-card {
        width: 40px !important;
        height: 56px !important;
    }

    /* Card passing selection area cards */
    .selected-cards .card {
        width: 32px !important;
        height: 45px !important;
        font-size: 7px !important;
    }

    /* Card passing slots */
    .passing-slot {
        width: 42px !important;
        height: 59px !important;
        padding: 2px;
    }

    /* Cards in passing slots */
    .passing-slot .card {
        width: 37px !important;
        height: 52px !important;
        font-size: 7px !important;
        padding: 1px !important;
        margin: 0 !important;
        position: relative !important;
        left: auto !important;
        top: auto !important;
        transform: none !important;
    }

    /* Trick area compression */
    .trick-area {
        width: 100px !important;
        height: 100px !important;
    }

    /* Player info area compression */
    .player-info {
        gap: 8px;
        font-size: 11px;
    }

    .player-name {
        font-size: 11px;
    }

    .player-score {
        font-size: 12px;
    }

    .cards-count {
        font-size: 10px;
    }

    /* Score panel optimization */
    .scores-panel {
        gap: 8px;
        padding: 5px;
    }

    .player-score .score-value {
        font-size: 14px;
    }

    /* Status bar optimization */
    .game-status {
        top: 3px;
        padding: 5px 12px;
        font-size: 12px;
    }

    .status-message {
        font-size: 12px;
    }

    /* Hand spacing minimization */
    .player-hand {
        gap: 1px;
    }

    /* Button size adjustment */
    .btn {
        padding: 6px 12px;
        font-size: 11px;
    }

    /* Passing modal adaptation */
    .passing-modal .modal-content {
        padding: 12px;
        max-width: 95%;
        max-height: 90vh;
    }

    .passing-slots {
        gap: 6px;
        padding: 10px;
    }

    .passing-instructions {
        font-size: 12px;
        margin-bottom: 8px;
    }

    /* Help panel adaptation */
    .help-panel .modal-content {
        max-height: 85vh;
        overflow-y: auto;
    }

    .help-section h3 {
        font-size: 14px;
    }

    .help-section p, .help-section li {
        font-size: 11px;
    }
}

/* Further optimization for smaller screen landscape */
@media (max-width: 640px) and (orientation: landscape) {
    .game-header {
        padding: 5px 10px;
    }

    .header-left h1 {
        font-size: 1rem;
    }

    .game-board {
        height: calc(100vh - 50px);
    }

    .center-area {
        min-height: 100px;
        max-height: 120px;
        padding: 0 5px;
    }

    .card {
        width: 35px !important;
        height: 49px !important;
        font-size: 8px !important;
        min-width: 35px;
        min-height: 49px;
    }

    /* Enhanced touch area for smaller screens */
    .card.selectable::before {
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
    }

    .card-center {
        font-size: 10px !important;
    }

    .played-card {
        width: 35px !important;
        height: 49px !important;
    }

    .trick-area {
        width: 80px !important;
        height: 80px !important;
    }

    .player-area.south .player-hand {
        min-height: 50px;
        max-height: 60px;
    }

    .player-area.north .player-hand {
        min-height: 40px;
        max-height: 50px;
    }

    .player-area.west .player-hand,
    .player-area.east .player-hand {
        min-width: 50px;
        max-width: 70px;
        min-height: 80px;
        max-height: 100px;
    }

    /* Passing slots for smaller screens */
    .passing-slot {
        width: 37px !important;
        height: 52px !important;
        padding: 1px;
    }

    .passing-slot .card {
        width: 32px !important;
        height: 45px !important;
        font-size: 6px !important;
        padding: 1px !important;
    }

    #fullscreenBtn {
        font-size: 12px;
        padding: 4px 8px;
        min-width: 32px;
    }
}

/* Spades-specific styles */
.bidding-slots {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(0,0,0,0.95) 0%, rgba(20,20,20,0.95) 100%);
    border-radius: 20px;
    padding: 30px;
    z-index: 10;
    min-width: 320px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    border: 1px solid rgba(255,255,255,0.1);
}

.bidding-header {
    text-align: center;
    margin-bottom: 25px;
    color: #FFD700;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.bidding-numbers {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 12px;
    margin-bottom: 25px;
    width: 100%;
    max-width: 400px;
}

.bidding-slot {
    width: 45px;
    height: 45px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.bidding-slot:hover {
    border-color: rgba(255,255,255,0.8);
    background: linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.bidding-slot.selected {
    border-color: #FFD700;
    background: linear-gradient(145deg, rgba(255,215,0,0.3), rgba(255,215,0,0.1));
    box-shadow: 0 0 15px rgba(255,215,0,0.4);
    transform: translateY(-2px);
    color: #fff;
}

.bidding-slot .slot-number {
    color: #FFD700;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.bidding-slot.selected .slot-number {
    color: #FFD700;
    text-shadow: 0 1px 3px rgba(255,215,0,0.5);
}

.bidding-button-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.bidding-button-container .btn {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 25px;
    background: linear-gradient(145deg, #4CAF50, #45a049);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76,175,80,0.3);
}

.bidding-button-container .btn:hover:not(:disabled) {
    background: linear-gradient(145deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76,175,80,0.4);
}

.bidding-button-container .btn:disabled {
    background: linear-gradient(145deg, #666, #555);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.bid-info {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
}

.bid-label {
    font-size: 12px;
    color: rgba(255,255,255,0.8);
}

.bid-value {
    font-size: 14px;
    font-weight: bold;
    color: #FFD700;
}

.tricks-label {
    font-size: 12px;
    color: rgba(255,255,255,0.8);
    margin-left: 10px;
}

.tricks-value {
    font-size: 14px;
    font-weight: bold;
    color: #00ff00;
}

.round-results {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255,255,255,0.05);
    border-radius: 8px;
}

.round-results h4 {
    margin: 0 0 15px 0;
    color: #FFD700;
    text-align: center;
}

.results-table {
    width: 100%;
}

.results-row {
    display: flex;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.results-row.header {
    font-weight: bold;
    color: #FFD700;
    border-bottom: 2px solid #FFD700;
}

.results-row .player-name {
    flex: 2;
    text-align: left;
}

.results-row .bid,
.results-row .tricks,
.results-row .points {
    flex: 1;
    text-align: center;
}

.results-row .points {
    font-weight: bold;
}

.bidding-info {
    margin-top: 10px;
    padding: 10px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
}

@media (max-width: 768px) {
    .bidding-slots {
        padding: 20px;
        max-width: 98%;
        min-width: 280px;
    }
    
    .bidding-header {
        font-size: 16px;
        margin-bottom: 20px;
    }
    
    .bidding-numbers {
        grid-template-columns: repeat(7, 1fr);
        gap: 8px;
        margin-bottom: 20px;
    }
    
    .bidding-slot {
        width: 35px;
        height: 35px;
        border-radius: 8px;
    }
    
    .bidding-slot .slot-number {
        font-size: 14px;
    }
    
    .bidding-button-container .btn {
        padding: 10px 25px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .bidding-slots {
        padding: 15px;
        max-width: 99%;
        min-width: 260px;
    }
    
    .bidding-header {
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    .bidding-numbers {
        grid-template-columns: repeat(7, 1fr);
        gap: 6px;
        margin-bottom: 15px;
    }
    
    .bidding-slot {
        width: 30px;
        height: 30px;
        border-radius: 6px;
    }
    
    .bidding-slot .slot-number {
        font-size: 12px;
    }
    
    .bidding-button-container .btn {
        padding: 8px 20px;
        font-size: 12px;
    }
}

/* 横屏模式的叫分界面优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .bidding-slots {
        padding: 15px;
        max-width: 90%;
        min-width: 400px;
    }
    
    .bidding-header {
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    .bidding-numbers {
        grid-template-columns: repeat(7, 1fr);
        gap: 6px;
        margin-bottom: 15px;
    }
    
    .bidding-slot {
        width: 32px;
        height: 32px;
        border-radius: 6px;
    }
    
    .bidding-slot .slot-number {
        font-size: 12px;
    }
    
    .bidding-button-container .btn {
        padding: 8px 20px;
        font-size: 12px;
    }
}
