/* Mahjong Connect - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a4d2e;
    color: #ffffff;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
}

/* Main Content Wrapper */
.main-content {
    display: flex;
    flex-direction: column;
    padding-top: 0;
}

/* Loading Screen */
.loading-screen {
    display: none;
}

.loading {
    color: white;
    font-size: 18px;
    font-weight: 500;
}

/* Landscape Prompt */
.landscape-prompt {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a4d2e 0%, #2d5a3d 100%);
    z-index: 10000;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
}

.landscape-content {
    max-width: 400px;
}

.rotate-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: rotate 2s infinite ease-in-out;
}

@keyframes rotate {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(90deg); }
}

.landscape-content h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.landscape-content p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
}

/* Game Container */
.game-container {
    display: flex;
    flex-direction: column;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    height: 100vh;
    height: 100dvh;
    background: #1a4d2e;
}

/* Game Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
}

.header-left h1 {
    font-size: 2rem;
    margin: 0 0 10px 0;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.game-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.game-stats span {
    background: rgba(255,255,255,0.2);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid rgba(255,255,255,0.3);
}

.header-right {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: linear-gradient(145deg, #4caf50 0%, #45a049 100%);
    border-color: #45a049;
}

.btn-secondary {
    background: linear-gradient(145deg, #757575 0%, #616161 100%);
    border-color: #616161;
}

.btn-home {
    background: linear-gradient(145deg, #2196F3 0%, #1976D2 100%);
    border-color: #1976D2;
}

.btn-restart {
    background: linear-gradient(145deg, #4CAF50 0%, #388E3C 100%);
    border-color: #388E3C;
}

.btn-rules {
    background: linear-gradient(145deg, #FF9800 0%, #F57C00 100%);
    border-color: #F57C00;
}

.btn-fullscreen {
    background: linear-gradient(145deg, #9C27B0 0%, #7B1FA2 100%);
    border-color: #7B1FA2;
}

/* Game Board */
.game-board {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
    height: 100%;
}

.mahjong-grid {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;
}

#mahjongGrid {
    position: relative;
    margin: 0 auto;
    width: 100%;
    max-width: 800px;
    min-height: 400px;
}

/* Game Messages */
.game-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.85);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.message-content {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    padding: 20px;
    border-radius: 24px;
    text-align: center;
    max-width: 520px;
    min-width: 400px;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow:
        0 32px 64px rgba(0,0,0,0.3),
        0 16px 32px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.8);
    transform: scale(0.9);
    animation: modalSlideIn 0.4s ease-out forwards;
    position: relative;
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.9) translateY(20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.message-content h2 {
    font-size: 2.8rem;
    margin-bottom: 10px;
    color: #2c3e50;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    letter-spacing: -0.02em;
}

.message-content p {
    font-size: 1.3rem;
    margin-bottom: 12px;
    color: #5a6c7d;
    line-height: 1.6;
    white-space: pre-line;
    font-weight: 400;
}

.message-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.message-buttons .btn {
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 120px;
    position: relative;
    overflow: hidden;
}

.message-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.message-buttons .btn:hover::before {
    left: 100%;
}

.message-buttons .btn-primary {
    background: linear-gradient(145deg, #4CAF50 0%, #45a049 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.message-buttons .btn-primary:hover {
    background: linear-gradient(145deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.5);
}

.message-buttons .btn-secondary {
    background: linear-gradient(145deg, #6c757d 0%, #5a6268 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.message-buttons .btn-secondary:hover {
    background: linear-gradient(145deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.5);
}

/* Message Type Specific Styles */
.message-content.message-success::before {
    background: linear-gradient(90deg, #4CAF50, #8BC34A, #CDDC39);
}

.message-content.message-success h2 {
    color: #2E7D32;
}

.message-content.message-error h2 {
    color: #C62828;
}

.message-content.message-warning::before {
    background: linear-gradient(90deg, #FF9800, #FFC107, #FFEB3B);
}

.message-content.message-warning h2 {
    color: #E65100;
}

/* Share Section Styles */
.share-section {
    margin: 10px 0;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.share-text {
    text-align: center;
    margin-bottom: 16px;
}

.share-text p {
    color: #5a6c7d;
    font-size: 1.1rem;
    margin: 0;
    font-weight: 500;
}

.share-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border: 2px solid rgba(76, 175, 80, 0.3);
    border-radius: 50%;
    background: rgba(76, 175, 80, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    padding: 0;
}

.share-button img {
    width: 28px;
    height: 28px;
    filter: brightness(0) saturate(100%) invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%);
    transition: filter 0.3s ease;
}

.share-button:hover {
    background: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.share-button:hover img {
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: linear-gradient(145deg, #2d5a3d 0%, #1a4d2e 100%);
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.modal-header h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid rgba(255,255,255,0.2);
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* Settings */
.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #ffffff;
}

.setting-group select,
.setting-group input[type="checkbox"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    background: rgba(255,255,255,0.1);
    color: white;
    font-size: 14px;
}

.setting-group input[type="checkbox"] {
    width: auto;
    transform: scale(1.2);
}

/* Rules Content */
.rules-content h4 {
    color: #ffffff;
    margin: 20px 0 10px 0;
    font-size: 1.2rem;
}

.rules-content p,
.rules-content li {
    color: rgba(255,255,255,0.9);
    margin-bottom: 8px;
    line-height: 1.6;
}

.rules-content ul {
    margin-left: 20px;
    margin-bottom: 20px;
}

/* SEO Content */
.seo-content-section {
    background: #1a4d2e;
    padding: 60px 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.game-description h2,
.game-description h3 {
    color: #ffffff;
    margin-bottom: 20px;
}

.game-description p,
.game-description li {
    color: rgba(255,255,255,0.9);
    margin-bottom: 15px;
    line-height: 1.7;
}

.game-description ul {
    margin-left: 20px;
    margin-bottom: 25px;
}



/* Responsive Design - Portrait only */
@media (max-width: 768px) and (orientation: portrait) {
    body {
        font-size: 14px;
    }

    .game-container {
        padding: 10px;
    }

    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 10px 15px;
    }

    .header-left h1 {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }

    .game-stats {
        justify-content: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    .game-stats span {
        font-size: 11px;
        padding: 3px 8px;
        min-width: 60px;
        text-align: center;
    }

    .header-right {
        justify-content: center;
        gap: 6px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 5px 10px;
        font-size: 11px;
        min-width: 32px;
        height: 32px;
        border-radius: 6px;
    }

    .game-board {
        padding: 10px 5px 20px 5px;
        min-height: 350px;
    }



    #mahjongGrid {
        transform: scale(0.8);
        transform-origin: center;
    }

    .message-content {
        padding: 35px 20px;
        margin: 20px;
        border-radius: 20px;
        min-width: 300px;
        max-width: 90vw;
    }

    .message-content h2 {
        font-size: 2.2rem;
        margin-bottom: 18px;
    }

    .message-content p {
        font-size: 1.1rem;
        margin-bottom: 28px;
    }

    .message-buttons .btn {
        padding: 12px 24px;
        font-size: 15px;
        min-width: 110px;
    }

    /* Share buttons mobile styles */
    .share-buttons {
        gap: 12px;
    }

    .share-button {
        width: 45px;
        height: 45px;
    }

    .share-button img {
        width: 24px;
        height: 24px;
    }

    .modal-content {
        margin: 15px;
        width: calc(100% - 30px);
        border-radius: 12px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }

    .modal-header h3 {
        font-size: 1.3rem;
    }

    .setting-group {
        margin-bottom: 15px;
    }

    .setting-group label {
        font-size: 14px;
        margin-bottom: 6px;
    }

    .setting-group select {
        padding: 6px 10px;
        font-size: 13px;
    }

    .recommendations-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .recommendation-card {
        padding: 20px;
    }

    .recommendation-card .game-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .recommendation-card .game-name {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) and (orientation: portrait) {
    body {
        font-size: 13px;
    }

    .game-container {
        padding: 5px;
    }

    .game-header {
        padding: 8px 10px;
        gap: 10px;
    }

    .header-left h1 {
        font-size: 1.3rem;
        margin-bottom: 5px;
    }

    .game-stats {
        flex-direction: column;
        gap: 5px;
        width: 100%;
    }

    .game-stats span {
        font-size: 10px;
        padding: 2px 6px;
        width: 100%;
        max-width: 120px;
        margin: 0 auto;
    }

    .header-right {
        flex-wrap: wrap;
        gap: 4px;
        justify-content: center;
    }

    .btn {
        min-width: 28px;
        height: 28px;
        padding: 3px 6px;
        font-size: 10px;
        border-radius: 4px;
    }

    .game-board {
        padding: 5px 2px 15px 2px;
        min-height: 300px;
    }



    #mahjongGrid {
        transform: scale(0.65);
        transform-origin: center;
    }

    .message-content {
        padding: 30px 16px;
        margin: 15px;
        min-width: 280px;
        max-width: 95vw;
    }

    .message-content h2 {
        font-size: 1.8rem;
        margin-bottom: 16px;
    }

    .message-content p {
        font-size: 1rem;
        margin-bottom: 24px;
        line-height: 1.5;
    }

    .message-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .message-buttons .btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 14px;
        min-width: auto;
    }

    .modal-content {
        margin: 10px;
        width: calc(100% - 20px);
        max-height: 85vh;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 12px;
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }

    .setting-group {
        margin-bottom: 12px;
    }

    .setting-group label {
        font-size: 13px;
        margin-bottom: 4px;
    }

    .setting-group select {
        padding: 5px 8px;
        font-size: 12px;
    }

    .rules-content h4 {
        font-size: 1.1rem;
        margin: 15px 0 8px 0;
    }

    .rules-content p,
    .rules-content li {
        font-size: 13px;
        line-height: 1.5;
    }

    .seo-content-section {
        padding: 40px 0;
    }

    .game-description h2,
    .game-description h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .game-description p,
    .game-description li {
        font-size: 13px;
        line-height: 1.6;
    }


}

/* Touch-specific optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn:hover {
        transform: none;
        background: rgba(255,255,255,0.2);
    }

    .btn:active {
        transform: scale(0.95);
        background: rgba(255,255,255,0.4);
    }

    .mahjong-tile:hover {
        transform: none;
        box-shadow:
            2px 2px 4px rgba(0,0,0,0.3),
            inset 0 0 0 1px rgba(255,255,255,0.8);
    }

    .mahjong-tile:active {
        transform: scale(0.95);
    }

    .mahjong-tile.disabled:active {
        transform: scale(0.95) !important;
    }

    .recommendation-card:hover {
        transform: none;
    }

    .recommendation-card:active {
        transform: scale(0.98);
    }
}

/* Mobile Portrait - Show landscape prompt */
@media screen and (max-width: 1024px) and (orientation: portrait) {
    .landscape-prompt {
        display: flex !important;
    }

    .game-container {
        display: none !important;
    }
}

/* 移动端竖屏时默认显示横屏提示，避免麻将牌闪现 */
@media screen and (max-width: 1024px) {
    .landscape-prompt {
        display: flex;
    }

    .game-container {
        display: none;
    }
}

/* Mobile Landscape - Hide prompt and optimize layout */
@media screen and (max-width: 1024px) and (orientation: landscape) and (-webkit-min-device-pixel-ratio: 2),
       screen and (max-width: 1024px) and (orientation: landscape) and (min-resolution: 192dpi) {
    .landscape-prompt {
        display: none !important;
    }

    .game-container {
        display: flex !important;
    }

    body {
        overflow: hidden;
        position: fixed;
        width: 100%;
        height: 100%;
    }

    .game-container {
        height: 100vh !important;
        height: 100dvh !important;
        padding: 5px !important;
        overflow: hidden !important;
        display: flex !important;
        flex-direction: column !important;
    }
    .game-board {
        flex: 1 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-height: 0 !important;
        padding: 1px !important;
        height: calc(100vh - 36px) !important;
        height: calc(100dvh - 36px) !important;
        position: relative !important;
    }

    .mahjong-tile {
        width: 32px !important;
        height: 42px !important;
        font-size: 24px !important;
    }

    .game-stats span{
        padding: 3px 8px;
    }

    .header-left h1{
        font-size: 1.2rem;
    }


    .mahjong-tile {
        width: 32px !important;
        height: 42px !important;
        font-size: 24px !important;
    }

    .tile-back {
        font-size: 18px !important;
    }

    /* Optimize modals for landscape mobile */
    .modal-content {
        max-width: 90vw;
        max-height: 85vh;
        margin: 5vh auto;
        padding: 15px;
    }

    .btn{
        font-size: 12px;
        padding: 3px 8px;
        height: 32px;
    }

    .modal-header {
        padding: 10px 15px;
    }

    .modal-header h3 {
        font-size: 1rem;
    }

    .modal-body {
        padding: 10px 15px;
        max-height: 55vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 8px 15px;
    }

    /* Game message optimization */
    .game-message .message-content {
        max-width: 90vw;
        max-height: 85vh;
        padding: 15px;
        margin: 5vh auto;
    }

    .game-message h2 {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }

    .game-message .message-stats {
        flex-direction: row;
        gap: 6px;
        margin: 8px 0;
        flex-wrap: wrap;
    }

    .game-message .message-stats div {
        padding: 4px 8px;
        font-size: 10px;
        flex: 1;
        min-width: 60px;
    }

    .game-message .message-buttons {
        gap: 6px;
        margin-top: 10px;
    }

    .game-message .message-buttons .btn {
        padding: 6px 12px;
        font-size: 11px;
    }

    /* Touch optimization */
    .btn {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .btn:active {
        transform: scale(0.95);
    }

    /* Prevent zoom and scrolling */
    * {
        touch-action: manipulation;
        -webkit-user-select: none;
        user-select: none;
    }

    .modal-body {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
        touch-action: pan-y;
    }

    /* Ensure no horizontal scrolling */
    html, body {
        overflow-x: hidden;
        max-width: 100vw;
    }

    /* Prevent pull-to-refresh */
    body {
        overscroll-behavior-y: contain;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn {
        border-width: 0.5px;
    }

    .game-header {
        padding: 5px 10px;
        margin-bottom: 0;
    }

    .modal-content {
        border-width: 1px;
    }
}
