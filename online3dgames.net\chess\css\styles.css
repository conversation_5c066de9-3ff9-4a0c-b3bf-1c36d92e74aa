* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Safe area support for notch and punch-hole displays */
html {
    width: 100vw;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
}

/* Support for devices with notches/punch holes */
@supports (padding: max(0px)) {
    body {
        padding-left: max(0px, env(safe-area-inset-left));
        padding-right: max(0px, env(safe-area-inset-right));
        padding-top: max(0px, env(safe-area-inset-top));
        padding-bottom: max(0px, env(safe-area-inset-bottom));
    }
}

.rotate-prompt {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    color: white;
}

.rotate-content {
    text-align: center;
    padding: 40px 20px;
}

.rotate-icon {
    font-size: 80px;
    margin-bottom: 20px;
    animation: rotatePhone 2s ease-in-out infinite;
}

.rotate-text {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    line-height: 1.5;
    max-width: 300px;
}

.rotate-arrow {
    font-size: 40px;
    animation: rotateArrow 1.5s ease-in-out infinite;
}

@keyframes rotatePhone {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(90deg); }
}

@keyframes rotateArrow {
    0%, 100% { transform: rotate(0deg); opacity: 0.7; }
    50% { transform: rotate(180deg); opacity: 1; }
}

@media screen and (max-width: 1024px) and (orientation: portrait) {
    .rotate-prompt {
        display: flex !important;
    }

    .chess-container {
        display: none !important;
    }
}

/* 移动端竖屏时默认显示横屏提示，避免棋盘闪现 */
@media screen and (max-width: 1024px) {
    .rotate-prompt {
        display: flex;
    }

    .chess-container {
        display: none;
    }
}

/* 移动端横屏时显示游戏内容 */
@media screen and (max-width: 1024px) and (orientation: landscape) {
    .rotate-prompt {
        display: none !important;
    }

    .chess-container {
        display: flex !important;
    }
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #1a4b3a 0%, #2d5a4a 50%, #1a4b3a 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.chess-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
}

.top-status {
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    margin: 0 auto;
}

.left-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.right-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.home-button {
    background: linear-gradient(145deg, #4f46e5, #3730a3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
    text-decoration: none;
}

.home-button:hover {
    background: linear-gradient(145deg, #5b52e8, #4338ca);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.fullscreen-button {
    background: rgba(0, 0, 0, 0.6);
    padding: 8px 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.fullscreen-button:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.fullscreen-icon {
    font-size: 18px;
    line-height: 35px;
    color: #fbbf24;
}

.audio-button {
    background: rgba(0, 0, 0, 0.6);
    padding: 8px 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
}

.audio-button:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.audio-icon {
    font-size: 18px;
    line-height: 35px;
}

.chess-table {
    position: relative;
    width: 100%;
    min-height: 100vh;
    overflow: visible;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.game-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 800px;
    width: 100%;
}

/* 确保game-status始终在最上方 */
.game-area .game-status {
    order: -1;
}

.game-status {
    background: rgba(0, 0, 0, 0.6);
    color: #ffffff;
    padding: 12px 24px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    min-width: 200px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

/* Check state animations */
.game-status.check-warning {
    animation: checkPulse 1.5s ease-in-out infinite;
    border-color: rgba(239, 68, 68, 0.8);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.game-status.checkmate-warning {
    animation: checkmatePulse 2s ease-in-out infinite;
    border-color: rgba(220, 38, 38, 0.8);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

@keyframes checkPulse {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6);
        transform: scale(1.02);
    }
}

@keyframes checkmatePulse {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.8);
        transform: scale(1.05);
    }
}

.chessboard-container {
    position: relative;
    margin: 20px 0;
}

.chessboard {
    width: 600px;
    height: 600px;
    border: 4px solid #8B4513;
    border-radius: 8px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 1fr);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 2px 8px rgba(255, 255, 255, 0.1);
    background: linear-gradient(145deg, #8B4513, #A0522D);
}

.chess-square {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 48px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    user-select: none;
}

.chess-square.white {
    background: linear-gradient(145deg, #F5DEB3, #DEB887);
}

.chess-square.black {
    background: linear-gradient(145deg, #8B4513, #654321);
}

.chess-square:hover {
    box-shadow: inset 0 0 0 3px rgba(255, 215, 0, 0.6);
    transform: scale(1.02);
}

.chess-square.selected {
    box-shadow: inset 0 0 0 4px #ffd700;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3), transparent);
}

.chess-square.possible-move {
    box-shadow: inset 0 0 0 3px rgba(34, 197, 94, 0.8);
}

.chess-square.possible-move::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background: rgba(34, 197, 94, 0.6);
    border-radius: 50%;
}

.chess-square.capture-move {
    box-shadow: inset 0 0 0 3px rgba(239, 68, 68, 0.8);
}

.chess-square.capture-move::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid rgba(239, 68, 68, 0.6);
    border-radius: 50%;
    box-sizing: border-box;
}

.chess-piece {
    font-size: 48px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
    cursor: pointer;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    position: relative;
    z-index: 10;
}

.chess-piece.moving {
    z-index: 100;
    transition: all 0.3s ease-out;
    transform: scale(1.2);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.6));
}

/* Cleaned up unused animations */

.chess-piece:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
}

.chess-piece.white {
    color: #ffffff;
    text-shadow: 
        2px 2px 4px rgba(0, 0, 0, 0.8),
        -1px -1px 2px rgba(0, 0, 0, 0.5);
}

.chess-piece.black {
    color: #333333;
    text-shadow: 
        2px 2px 4px rgba(255, 255, 255, 0.3),
        -1px -1px 2px rgba(0, 0, 0, 0.8);
}

.player-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 600px;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 10px 0;
}

/* 默认布局：白棋玩家时 */
.game-area .player-info.white-player {
    order: 2; /* 白棋信息在下方 */
}

.game-area .player-info.black-player {
    order: 0; /* 黑棋信息在上方 */
}

.game-area .chessboard-container {
    order: 1; /* 棋盘在中间 */
}

/* 当玩家选择黑棋时，完全翻转布局 */
.chess-container.player-black .game-area .player-info.white-player {
    order: 0 !important; /* 白棋信息在上方 */
}

.chess-container.player-black .game-area .player-info.black-player {
    order: 2 !important; /* 黑棋信息在下方 */
}

/* 额外的强制规则，确保布局正确 */
body .chess-container.player-black .game-area .player-info.white-player {
    order: 0 !important;
}

body .chess-container.player-black .game-area .player-info.black-player {
    order: 2 !important;
}

/* 默认状态的强制规则 */
body .chess-container.player-white .game-area .player-info.white-player,
body .chess-container:not(.player-black) .game-area .player-info.white-player {
    order: 2 !important;
}

body .chess-container.player-white .game-area .player-info.black-player,
body .chess-container:not(.player-black) .game-area .player-info.black-player {
    order: 0 !important;
}

/* 棋盘翻转样式 */
.chessboard.flipped {
    transform: rotate(180deg);
}

.chessboard.flipped .chess-piece {
    transform: rotate(180deg);
}

/* 当玩家是黑棋时，玩家信息也进行相应的视觉调整 */
.chess-container.player-black .player-info {
    /* 可以添加一些视觉指示，表明布局已翻转 */
}

/* 确保玩家自己的信息有特殊标识 */
.player-info.player-self {
    border: 2px solid rgba(255, 215, 0, 0.4);
    background: rgba(255, 215, 0, 0.1);
}

.player-info.player-opponent {
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.player-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.player-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(145deg, #4f46e5, #3730a3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.player-details.active .player-avatar {
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    animation: playerGlow 2s ease-in-out infinite alternate;
}

@keyframes playerGlow {
    0% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    }
    100% {
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.9);
    }
}

.player-name {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.player-details.active .player-name {
    color: #ffd700;
}

.captured-pieces {
    display: flex;
    gap: 3px;
    flex-wrap: wrap;
    min-height: 35px;
    align-items: center;
    justify-content: flex-start;
    padding: 5px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.captured-piece {
    font-size: 28px;
    opacity: 0.8;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    margin: 2px;
    display: inline-block;
    transition: all 0.3s ease;
}

.captured-piece:hover {
    opacity: 1;
    transform: scale(1.1);
}

.game-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 100;
    padding: 15px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.control-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 120px;
    justify-content: flex-start;
    white-space: nowrap;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.new-game-btn {
    background: linear-gradient(145deg, #10b981, #059669);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.undo-btn {
    background: linear-gradient(145deg, #f59e0b, #d97706);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.settings-btn {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.game-settings {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.95) 0%, rgba(40, 40, 40, 0.9) 100%);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 215, 0, 0.2);
    text-align: center;
    min-width: 400px;
    z-index: 1000;
    display: block;
}

.settings-title {
    color: #ffd700;
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 30px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.setting-group {
    margin-bottom: 25px;
}

.setting-label {
    display: block;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.setting-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.setting-btn {
    background: linear-gradient(145deg, rgba(60, 60, 60, 0.9) 0%, rgba(40, 40, 40, 0.9) 100%);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #ffffff;
    font-weight: 600;
    min-width: 120px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.setting-btn:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4), 0 0 15px rgba(255, 215, 0, 0.3);
}

.setting-btn.active {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.9) 0%, rgba(255, 193, 7, 0.8) 100%);
    border-color: rgba(255, 215, 0, 0.8);
    color: #1a1a1a;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4), 0 0 20px rgba(255, 215, 0, 0.5);
}

.start-game-btn {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.95) 0%, rgba(255, 193, 7, 0.9) 50%, rgba(255, 152, 0, 0.85) 100%);
    border: 3px solid rgba(255, 215, 0, 0.8);
    border-radius: 20px;
    padding: 20px 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 215, 0, 0.4);
    min-width: 200px;
    animation: startBtnPulse 2s ease-in-out infinite;
}

.start-game-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 215, 0, 0.6);
}

.btn-text {
    font-size: 24px;
    font-weight: 900;
    color: #1a1a1a;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
}

@keyframes startBtnPulse {
    0%, 100% {
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 215, 0, 0.4);
    }
    50% {
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4), 0 0 35px rgba(255, 215, 0, 0.7);
    }
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: none;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: linear-gradient(145deg, #1e293b, #334155);
    margin: 10% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 450px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    padding: 25px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    color: #ffd700;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.modal-body {
    padding: 25px;
    color: #e5e7eb;
    text-align: center;
}

.modal-footer {
    padding: 20px 25px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 10px;
}

.modal-btn {
    flex: 1;
    padding: 15px 25px;
    border: none;
    border-radius: 10px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
}

.modal-btn.primary {
    background: linear-gradient(145deg, #059669, #047857);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.modal-btn.secondary {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    color: #ffffff;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Responsive design */
@media screen and (max-width: 768px) {
    .chessboard {
        width: 80vmin;
        height: 80vmin;
        max-width: 400px;
        max-height: 400px;
    }
    
    .chess-piece {
        font-size: 6vmin;
    }
    
    .player-info {
        width: 80vmin;
        max-width: 400px;
        padding: 10px 15px;
    }
    
    .game-controls {
        bottom: 10px;
        right: 10px;
        padding: 10px;
        gap: 8px;
    }
    
    .control-btn {
        padding: 8px 12px;
        font-size: 11px;
        min-width: 100px;
    }
    
    .chess-square.possible-move::after {
        width: 15px;
        height: 15px;
    }
    
    .captured-pieces {
        max-width: 180px;
        min-height: 30px;
    }
    
    .captured-piece {
        font-size: 22px;
        margin: 1px;
    }
}

@media screen and (max-width: 1024px) and (orientation: landscape) {
    .chess-table {
        padding: 10px;
    }
    
    .chessboard {
        width: 70vh;
        height: 70vh;
    }
    
    .player-info {
        width: 70vh;
        margin: 5px 0;
    }
    
    .game-controls {
        bottom: 5px;
        right: 5px;
        padding: 8px;
        gap: 6px;
    }
    
    .control-btn {
        padding: 6px 10px;
        font-size: 10px;
        min-width: 90px;
    }
    
    .captured-pieces {
        max-width: 200px;
        min-height: 28px;
    }
    
    .captured-piece {
        font-size: 20px;
    }
} 