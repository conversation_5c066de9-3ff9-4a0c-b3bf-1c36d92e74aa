$(document).ready(function() {
    const SUITS = ['hearts', 'diamonds', 'clubs', 'spades'];
    const RANKS = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    const SUIT_SYMBOLS = {
        hearts: '♥',
        diamonds: '♦',
        clubs: '♣',
        spades: '♠'
    };
    
    let gameState = {
        players: ['south', 'west', 'north', 'east'],
        hands: { south: [], west: [], north: [], east: [] },
        scores: { south: 0, west: 0, north: 0, east: 0 },
        currentRoundScores: { south: 0, west: 0, north: 0, east: 0 },
        previousRoundScores: { south: 0, west: 0, north: 0, east: 0 },
        scoreHistory: [],
        currentRound: 1,
        currentTrick: 1,
        trickCards: {},
        currentPlayer: 'south',
        leadPlayer: 'south',
        spadesBroken: false,
        biddingPhase: false,
        bids: { south: -1, west: -1, north: -1, east: -1 },
        tricksWon: { south: 0, west: 0, north: 0, east: 0 },
        selectedBid: null,
        playedCards: [],
        gameOver: false,
        trickWinner: null,
        animatingCards: 0,
        playerHasPlayedThisTrick: false,
        playedCardsHistory: [],
        aiThinking: false,
        aiMemory: {
            west: { difficulty: 'intermediate', personality: 'balanced' },
            north: { difficulty: 'advanced', personality: 'aggressive' },
            east: { difficulty: 'advanced', personality: 'conservative' }
        },
        hasAutoFullscreened: false
    };

    const domCache = {
        players: {},
        statusMessage: null,
        currentRound: null,
        currentTrick: null,
        scoreboardModal: null,
        scoreboardBody: null,
        continueBtn: null,
        newGameBtn: null,
        gameResumeModal: null,
        resumeGameBtn: null,
        startNewGameBtn: null,
        gameSummary: null,
        resumeSummary: null,
        resumeScoreboard: null
    };



    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    function analyzeGameState(player) {
        const analysis = {
            tricksRemaining: 13 - gameState.currentTrick + 1,
            spadesPlayed: gameState.playedCardsHistory.filter(c => c.suit === 'spades').length,
            highSpadesPlayed: gameState.playedCardsHistory.filter(c => c.suit === 'spades' && ['K', 'A'].includes(c.rank)).length,
            playerScores: { ...gameState.scores },
            currentRoundScores: { ...gameState.currentRoundScores },
            bids: { ...gameState.bids },
            tricksWon: { ...gameState.tricksWon },
            isEarlyGame: gameState.currentTrick <= 4,
            isMidGame: gameState.currentTrick > 4 && gameState.currentTrick <= 10,
            isEndGame: gameState.currentTrick > 10
        };

        analysis.myPosition = gameState.players.indexOf(player);
        analysis.scoreRanking = [...gameState.players].sort((a, b) => gameState.scores[a] - gameState.scores[b]);
        analysis.isLeading = analysis.scoreRanking[0] === player;
        analysis.isLosing = analysis.scoreRanking[3] === player;

        // In Spades, shooting moon concept doesn't apply
        analysis.shootingMoonPossible = false;
        analysis.someoneShootingMoon = gameState.players.some(p =>
            p !== player && gameState.currentRoundScores[p] === 0 && analysis.tricksRemaining < 8
        );

        return analysis;
    }

    function analyzeHand(hand) {
        const analysis = {
            spades: hand.filter(c => c.suit === 'spades'),
            hearts: hand.filter(c => c.suit === 'hearts'),
            clubs: hand.filter(c => c.suit === 'clubs'),
            diamonds: hand.filter(c => c.suit === 'diamonds'),
            hasQueenOfSpades: hand.some(c => c.suit === 'spades' && c.rank === 'Q'),
            highSpades: hand.filter(c => c.suit === 'spades' && ['K', 'A'].includes(c.rank)),
            voidSuits: [],
            shortSuits: [],
            longSuits: []
        };

        ['spades', 'hearts', 'clubs', 'diamonds'].forEach(suit => {
            const count = analysis[suit].length;
            if (count === 0) {
                analysis.voidSuits.push(suit);
            } else if (count <= 2) {
                analysis.shortSuits.push(suit);
            } else if (count >= 5) {
                analysis.longSuits.push(suit);
            }
        });

        // In Spades, shooting moon concept doesn't apply
        analysis.shootingMoonViable = false;

        return analysis;
    }

    function initDOMCache() {
        domCache.statusMessage = $('#statusMessage');
        domCache.currentRound = $('#currentRound');
        domCache.currentTrick = $('#currentTrick');
        domCache.scoreboardModal = $('#scoreboardModal');
        domCache.scoreboardBody = $('#scoreboardBody');
        domCache.continueBtn = $('#continueBtn');
        domCache.scoreboardNewGameBtn = $('#scoreboardNewGameBtn');
        domCache.gameResumeModal = $('#gameResumeModal');
        domCache.resumeGameBtn = $('#resumeGameBtn');
        domCache.startNewGameBtn = $('#startNewGameBtn');
        domCache.gameSummary = $('#gameSummary');
        domCache.resumeSummary = $('#resumeSummary');
        domCache.resumeScoreboard = $('#resumeScoreboard');

        gameState.players.forEach(player => {
            domCache.players[player] = {
                hand: $(`#player-${player} .player-hand`),
                score: $(`#score-${player}`),
                cardsCount: $(`#player-${player} .cards-count`),
                playedCard: $(`#played-${player}`)
            };
        });
    }

    function saveGameState() {
        try {
            if (gameState.scoreHistory.length === 0) {
                return;
            }

            const gameData = {
                scoreHistory: [...gameState.scoreHistory],
                scores: { ...gameState.scores },
                currentRound: gameState.currentRound,
                timestamp: Date.now(),
                version: '2.0'
            };
            localStorage.setItem('spades-game-state', JSON.stringify(gameData));
        } catch (error) {
            console.warn('Failed to save game state:', error);
        }
    }

    function loadGameState() {
        try {
            const savedData = localStorage.getItem('spades-game-state');
            if (!savedData) return null;

            const gameData = JSON.parse(savedData);
            if (!gameData.scoreHistory || !gameData.version) return null;

            const timeDiff = Date.now() - gameData.timestamp;
            if (timeDiff > 7 * 24 * 60 * 60 * 1000) {
                clearGameState();
                return null;
            }

            if (gameData.scoreHistory.length === 0) {
                clearGameState();
                return null;
            }

            const maxScore = Math.max(...Object.values(gameData.scores));
            if (maxScore >= 500) {
                clearGameState();
                return null;
            }

            return gameData;
        } catch (error) {
            console.warn('Failed to load game state:', error);
            clearGameState();
            return null;
        }
    }

    function clearGameState() {
        try {
            localStorage.removeItem('spades-game-state');
        } catch (error) {
            console.warn('Failed to clear game state:', error);
        }
    }

    function createDeck() {
        const deck = [];
        SUITS.forEach(suit => {
            RANKS.forEach(rank => {
                deck.push({ suit, rank });
            });
        });
        return shuffleDeck(deck);
    }
    
    function shuffleDeck(deck) {
        for (let i = deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [deck[i], deck[j]] = [deck[j], deck[i]];
        }
        return deck;
    }
    
    function dealCards() {
        const deck = createDeck();
        gameState.players.forEach((player, index) => {
            gameState.hands[player] = deck.slice(index * 13, (index + 1) * 13);
            sortHand(gameState.hands[player]);
        });

        gameState.currentRoundScores = { south: 0, west: 0, north: 0, east: 0 };

        const twoOfClubs = gameState.hands.south.find(card =>
            card.suit === 'clubs' && card.rank === '2'
        );
        if (twoOfClubs) {
            gameState.leadPlayer = 'south';
            gameState.currentPlayer = 'south';
        } else {
            for (let player of gameState.players) {
                const hasTwo = gameState.hands[player].find(card =>
                    card.suit === 'clubs' && card.rank === '2'
                );
                if (hasTwo) {
                    gameState.leadPlayer = player;
                    gameState.currentPlayer = player;
                    break;
                }
            }
        }
    }
    
    function sortHand(hand) {
        const suitOrder = { clubs: 0, diamonds: 1, spades: 2, hearts: 3 };
        const rankOrder = { '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, 
                           '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14 };
        
        hand.sort((a, b) => {
            if (suitOrder[a.suit] !== suitOrder[b.suit]) {
                return suitOrder[a.suit] - suitOrder[b.suit];
            }
            return rankOrder[a.rank] - rankOrder[b.rank];
        });
    }
    
    function createCardElement(card, faceDown = false) {
        const cardEl = $('<div>').addClass('card');
        
        if (faceDown) {
            cardEl.addClass('face-down');
            return cardEl;
        }
        
        const isRed = card.suit === 'hearts' || card.suit === 'diamonds';
        cardEl.addClass(isRed ? 'red' : 'black');
        cardEl.data('card', card);
        
        const symbol = SUIT_SYMBOLS[card.suit];
        const topEl = $('<div>').addClass('card-top').html(`${card.rank}<span>${symbol}</span>`);
        const centerEl = $('<div>').addClass('card-center').text(symbol);
        const bottomEl = $('<div>').addClass('card-bottom').html(`${card.rank}<span>${symbol}</span>`);
        
        cardEl.append(topEl, centerEl, bottomEl);
        return cardEl;
    }
    
    function renderHands(animate = false) {
        const updates = [];

        gameState.players.forEach(player => {
            const handEl = domCache.players[player]?.hand || $(`#player-${player} .player-hand`);
            const cards = gameState.hands[player];
            const cardCount = cards.length;

            if (animate && player === 'south') {
                animateHandRearrangement(handEl, cards, player);
            } else {
                handEl.empty();
                const cardElements = [];

                cards.forEach((card, index) => {
                    const cardEl = createCardElement(card, player !== 'south');

                    if (player === 'south') {
                        cardEl.addClass('selectable');
                        cardEl.data('card', card);
                        cardEl.off('click').on('click', () => handleCardClick(card, cardEl));
                    }

                    arrangeCardInFan(cardEl, index, cardCount, player);
                    cardElements.push(cardEl[0]);
                });

                cardElements.forEach(el => handEl.append(el));
            }

            updates.push({
                element: domCache.players[player]?.cardsCount || $(`#player-${player} .cards-count`),
                value: gameState.hands[player].length
            });
        });

        requestAnimationFrame(() => {
            updates.forEach(update => update.element.text(update.value));
            if (!gameState.passingPhase && gameState.currentPlayer === 'south') {
                updatePlayableCards();
            }
        });
    }

    function getCardDimensions() {
        const isMobile = window.innerWidth <= 768;
        const isLandscape = window.innerWidth > window.innerHeight;
        const isLandscapeMobile = isMobile && isLandscape;

        if (isLandscapeMobile) {
            if (window.innerWidth <= 640) {
                return { width: 35, height: 49 };
            } else {
                return { width: 40, height: 56 };
            }
        } else if (window.innerWidth <= 480) {
            return { width: 45, height: 63 };
        } else if (window.innerWidth <= 768) {
            return { width: 50, height: 70 };
        } else {
            return { width: 75, height: 105 };
        }
    }

    function getPassingSlotCardDimensions() {
        const isMobile = window.innerWidth <= 1024;
        const isLandscape = window.innerWidth > window.innerHeight;
        const isLandscapeMobile = isMobile && isLandscape;

        if (isLandscapeMobile) {
            if (window.innerWidth <= 1024) {
                return { width: 60, height: 86 };
            } else {
                return { width: 37, height: 52 };
            }
        } else if (window.innerWidth <= 480) {
            return { width: 45, height: 63 };
        } else if (window.innerWidth <= 768) {
            return { width: 50, height: 70 };
        } else {
            return { width: 70, height: 98 };
        }
    }

    function arrangeCardInFan(cardEl, index, totalCards, player, animate = false) {
        const isMobile = window.innerWidth <= 768;
        const isLandscape = window.innerWidth > window.innerHeight;
        const isLandscapeMobile = isMobile && isLandscape;

        if (totalCards === 1) {
            const styles = {
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 1
            };

            if (animate) {
                cardEl.css('transition', 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)');
                setTimeout(() => cardEl.css(styles), 10);
            } else {
                cardEl.css(styles);
            }
            return;
        }

        const centerIndex = (totalCards - 1) / 2;
        const relativeIndex = index - centerIndex;

        let transform, left, top, maxAngle, radius;

        switch (player) {
            case 'south':
                if (isLandscapeMobile) {
                    maxAngle = 15;
                    const spreadStepSouth = Math.min(15, 120 / (totalCards - 1));
                    const angleStepSouth = Math.min(maxAngle / (totalCards - 1), 2);
                    const angleSouth = relativeIndex * angleStepSouth;
                    const spreadSouth = relativeIndex * spreadStepSouth;
                    const verticalOffsetSouth = Math.pow(Math.abs(angleSouth) / maxAngle, 1.5) * 12;
                    left = `calc(50% + ${spreadSouth}px)`;
                    top = `calc(50% + ${verticalOffsetSouth}px)`;
                    transform = `translate(-50%, -50%) rotate(${angleSouth}deg)`;
                } else {
                    maxAngle = isMobile ? 20 : 25;
                    const spreadStepSouth = Math.min(isMobile ? 20 : 30, (isMobile ? 180 : 300) / (totalCards - 1));
                    const angleStepSouth = Math.min(maxAngle / (totalCards - 1), isMobile ? 3 : 2);
                    const angleSouth = relativeIndex * angleStepSouth;
                    const spreadSouth = relativeIndex * spreadStepSouth;
                    const verticalOffsetSouth = Math.pow(Math.abs(angleSouth) / maxAngle, 1.5) * (isMobile ? 20 : 50);
                    left = `calc(50% + ${spreadSouth}px)`;
                    top = `calc(50% + ${verticalOffsetSouth}px)`;
                    transform = `translate(-50%, -50%) rotate(${angleSouth}deg)`;
                }
                break;

            case 'north':
                if (isLandscapeMobile) {
                    maxAngle = 25;
                    radius = 100;
                    const angleStepNorth = Math.min(maxAngle / (totalCards - 1), 4);
                    const angleNorth = relativeIndex * angleStepNorth;
                    const radiansNorth = (angleNorth * Math.PI) / 180;
                    const xOffsetNorth = Math.sin(radiansNorth) * radius * 0.25;
                    const yOffsetNorth = Math.cos(radiansNorth) * radius * 0.08 - 15;
                    left = `calc(50% + ${xOffsetNorth}px)`;
                    top = `calc(50% + ${yOffsetNorth}px)`;
                    transform = `translate(-50%, -50%) rotate(${-angleNorth}deg)`;
                } else {
                    maxAngle = isMobile ? 35 : 45;
                    radius = isMobile ? 160 : 220;
                    const angleStepNorth = Math.min(maxAngle / (totalCards - 1), isMobile ? 6 : 7);
                    const angleNorth = relativeIndex * angleStepNorth;
                    const radiansNorth = (angleNorth * Math.PI) / 180;
                    const xOffsetNorth = Math.sin(radiansNorth) * radius * 0.3;
                    const yOffsetNorth = Math.cos(radiansNorth) * radius * 0.1 - (isMobile ? 20 : 30);
                    left = `calc(50% + ${xOffsetNorth}px)`;
                    top = `calc(50% + ${yOffsetNorth}px)`;
                    transform = `translate(-50%, -50%) rotate(${-angleNorth}deg)`;
                }
                break;

            case 'west':
                if (isLandscapeMobile) {
                    maxAngle = 25;
                    radius = 80;
                    const angleStepWest = Math.min(maxAngle / (totalCards - 1), 4);
                    const angleWest = relativeIndex * angleStepWest;
                    const radiansWest = (angleWest * Math.PI) / 180;
                    const yOffsetWest = Math.sin(radiansWest) * radius * 0.25;
                    const xOffsetWest = Math.cos(radiansWest) * radius * 0.01;
                    left = `calc(50% + ${xOffsetWest}px)`;
                    top = `calc(50% + ${yOffsetWest}px)`;
                    transform = `translate(-50%, -50%) rotate(${90 + angleWest}deg)`;
                } else {
                    maxAngle = isMobile ? 35 : 45;
                    radius = isMobile ? 160 : 220;
                    const angleStepWest = Math.min(maxAngle / (totalCards - 1), isMobile ? 6 : 7);
                    const angleWest = relativeIndex * angleStepWest;
                    const radiansWest = (angleWest * Math.PI) / 180;
                    const yOffsetWest = Math.sin(radiansWest) * radius * 0.3;
                    const xOffsetWest = Math.cos(radiansWest) * radius * 0.02;
                    left = `calc(50% + ${xOffsetWest}px)`;
                    top = `calc(50% + ${yOffsetWest}px)`;
                    transform = `translate(-50%, -50%) rotate(${90 + angleWest}deg)`;
                }
                break;

            case 'east':
                if (isLandscapeMobile) {
                    maxAngle = 25;
                    radius = 80;
                    const angleStepEast = Math.min(maxAngle / (totalCards - 1), 4);
                    const angleEast = relativeIndex * angleStepEast;
                    const radiansEast = (angleEast * Math.PI) / 180;
                    const yOffsetEast = Math.sin(radiansEast) * radius * 0.25;
                    const xOffsetEast = -Math.cos(radiansEast) * radius * 0.01;
                    left = `calc(50% + ${xOffsetEast}px)`;
                    top = `calc(50% + ${yOffsetEast}px)`;
                    transform = `translate(-50%, -50%) rotate(${-90 - angleEast}deg)`;
                } else {
                    maxAngle = isMobile ? 35 : 45;
                    radius = isMobile ? 160 : 220;
                    const angleStepEast = Math.min(maxAngle / (totalCards - 1), isMobile ? 6 : 7);
                    const angleEast = relativeIndex * angleStepEast;
                    const radiansEast = (angleEast * Math.PI) / 180;
                    const yOffsetEast = Math.sin(radiansEast) * radius * 0.3;
                    const xOffsetEast = -Math.cos(radiansEast) * radius * 0.02;
                    left = `calc(50% + ${xOffsetEast}px)`;
                    top = `calc(50% + ${yOffsetEast}px)`;
                    transform = `translate(-50%, -50%) rotate(${-90 - angleEast}deg)`;
                }
                break;
        }

        const styles = {
            left: left,
            top: top,
            transform: transform,
            zIndex: index + 1
        };

        if (animate) {
            cardEl.css('transition', 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)');
            setTimeout(() => cardEl.css(styles), 10);
        } else {
            cardEl.css(styles);
        }
    }

    function animateHandRearrangement(handEl, newCards, player) {
        const existingCards = handEl.find('.card');
        const cardCount = newCards.length;

        const existingCardData = [];
        existingCards.each(function() {
            const cardData = $(this).data('card');
            if (cardData) {
                existingCardData.push({
                    element: $(this),
                    card: cardData
                });
            }
        });

        const cardsToKeep = [];
        const cardsToAdd = [];

        newCards.forEach((card, index) => {
            const existingCard = existingCardData.find(item =>
                item.card.suit === card.suit && item.card.rank === card.rank
            );

            if (existingCard) {
                cardsToKeep.push({
                    element: existingCard.element,
                    card: card,
                    newIndex: index
                });
            } else {
                cardsToAdd.push({
                    card: card,
                    newIndex: index
                });
            }
        });

        existingCards.each(function() {
            const cardData = $(this).data('card');
            const isKept = cardsToKeep.some(item =>
                item.card.suit === cardData.suit && item.card.rank === cardData.rank
            );
            if (!isKept) {
                $(this).css('opacity', '0');
                setTimeout(() => $(this).remove(), 100);
            }
        });

        cardsToKeep.forEach(item => {
            arrangeCardInFan(item.element, item.newIndex, cardCount, player, true);
        });

        cardsToAdd.forEach(item => {
            const cardEl = createCardElement(item.card, player !== 'south');

            if (player === 'south') {
                cardEl.addClass('selectable');
                cardEl.on('click', () => handleCardClick(item.card, cardEl));
            }

            cardEl.css('opacity', '0');
            handEl.append(cardEl);

            arrangeCardInFan(cardEl, item.newIndex, cardCount, player);

            setTimeout(() => {
                cardEl.css({
                    'opacity': '1',
                    'transition': 'opacity 0.3s ease'
                });
            }, 200);
        });
    }

    function handleCardClick(card, cardEl) {
        if (gameState.biddingPhase) {
            return; // No card selection during bidding
        } else if (gameState.passingPhase) {
            handlePassingCardClick(card, cardEl);
        } else if (gameState.currentPlayer === 'south') {
            handlePlayCardClick(card, cardEl);
        }
    }
    
    function handlePassingCardClick(card, cardEl) {
        const cardIndex = gameState.selectedCards.findIndex(c =>
            c.suit === card.suit && c.rank === card.rank
        );

        if (cardIndex !== -1) {
            gameState.selectedCards.splice(cardIndex, 1);
            gameState.hands.south.push(card);
            sortHand(gameState.hands.south);
            updatePassingInterface();
            renderHands(true);
        } else if (gameState.selectedCards.length + gameState.animatingCards < 3) {
            const hand = gameState.hands.south;
            const handIndex = hand.findIndex(c => c.suit === card.suit && c.rank === card.rank);
            if (handIndex !== -1) {
                const targetSlotIndex = gameState.selectedCards.length + gameState.animatingCards + 1;
                gameState.animatingCards++;

                const cardRect = cardEl[0].getBoundingClientRect();

                hand.splice(handIndex, 1);

                renderHands(true);

                animateCardToSlotFromPosition(cardRect, card, targetSlotIndex, () => {
                    gameState.selectedCards.push(card);
                    gameState.animatingCards--;
                    updatePassingInterface();
                });
            }
        }
    }
    
    function animateCardToSlotFromPosition(startRect, card, targetSlotIndex, callback) {
        const targetSlot = $(`.passing-slot[data-slot="${targetSlotIndex}"]`);

        if (targetSlot.length === 0) {
            console.error(`Target slot ${targetSlotIndex} not found`);
            if (callback) callback();
            return;
        }

        const slotRect = targetSlot[0].getBoundingClientRect();

        const cardDims = getPassingSlotCardDimensions();
        const targetWidth = cardDims.width;
        const targetHeight = cardDims.height;

        const targetLeft = slotRect.left + (slotRect.width - targetWidth) / 2;
        const targetTop = slotRect.top + (slotRect.height - targetHeight) / 2;

        const flyingCard = createCardElement(card);
        flyingCard.addClass('flying-card');

        const isMobile = window.innerWidth <= 768;
        const animationDuration = isMobile ? '0.5s' : '0.6s';
        const easingFunction = 'cubic-bezier(0.25, 0.46, 0.45, 0.94)';

        flyingCard.css({
            position: 'fixed',
            left: startRect.left,
            top: startRect.top,
            width: startRect.width + 'px',
            height: startRect.height + 'px',
            zIndex: 9999,
            transition: `all ${animationDuration} ${easingFunction}`,
            transformOrigin: 'center center',
            transform: 'none',
            margin: '0',
            padding: '5px',
            boxSizing: 'border-box',
            pointerEvents: 'none',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
        });

        $('body').append(flyingCard);

        requestAnimationFrame(() => {
            requestAnimationFrame(() => {
                flyingCard.css({
                    left: targetLeft + 'px',
                    top: targetTop + 'px',
                    width: targetWidth + 'px',
                    height: targetHeight + 'px'
                });
            });
        });

        let callbackExecuted = false;

        const executeCallback = () => {
            if (!callbackExecuted && callback) {
                callbackExecuted = true;
                callback();
            }
        };

        flyingCard.on('transitionend', () => {
            flyingCard.remove();
            executeCallback();
        });

        const timeoutDuration = isMobile ? 550 : 650;
        setTimeout(() => {
            flyingCard.remove();
            executeCallback();
        }, timeoutDuration);
    }

    function handlePlayCardClick(card, cardEl) {
        if (gameState.playerHasPlayedThisTrick) {
            return;
        }

        if (gameState.currentPlayer !== 'south') {
            return;
        }

        if (cardEl.hasClass('unplayable')) {
            return;
        }

        if (isValidPlay(card)) {
            gameState.playerHasPlayedThisTrick = true;
            playCard('south', card);
        }
    }
    
    function isValidPlay(card, player = 'south') {
        const leadSuit = gameState.playedCards.length > 0 ? gameState.playedCards[0].suit : null;
        const hand = gameState.hands[player];

        // First trick must be led with 2 of clubs
        if (gameState.currentTrick === 1 && gameState.playedCards.length === 0) {
            return card.suit === 'clubs' && card.rank === '2';
        }

        // Must follow suit if possible
        if (leadSuit) {
            const hasSuit = hand.some(c => c.suit === leadSuit);
            if (hasSuit && card.suit !== leadSuit) {
                return false;
            }
        }

        return true;
    }
    
    function playCard(player, card) {
        if (player !== gameState.currentPlayer) {
            console.error(`Invalid play: ${player} tried to play but current player is ${gameState.currentPlayer}`);
            return;
        }

        if (gameState.trickCards[player]) {
            console.error(`Player ${player} already played this trick`);
            return;
        }

        if (gameState.playedCards.length >= 4) {
            console.error('Trick already complete, cannot play more cards');
            return;
        }

        const hand = gameState.hands[player];
        const cardIndex = hand.findIndex(c => c.suit === card.suit && c.rank === card.rank);

        if (cardIndex === -1) {
            console.error(`Player ${player} does not have card ${card.rank} of ${card.suit}`);
            return;
        }



        gameState.playedCards.push(card);
        gameState.trickCards[player] = card;
        gameState.playedCardsHistory.push(card);

        if (card.suit === 'spades') {
            gameState.spadesBroken = true;
        }

        if (player === 'south') {
            const sourceEl = $(`#player-${player} .player-hand`);
            const sourceCard = sourceEl.find('.card').filter(function() {
                const cardData = $(this).data('card');
                return cardData && cardData.suit === card.suit && cardData.rank === card.rank;
            }).first();

            if (sourceCard.length > 0) {
                const cardRect = sourceCard[0].getBoundingClientRect();
                hand.splice(cardIndex, 1);
                renderHands(true);
                animateCardToCenter(cardRect, card, player);
            } else {
                hand.splice(cardIndex, 1);
                renderPlayedCardFallback(player, card);
            }
        } else {
            hand.splice(cardIndex, 1);
            renderPlayedCardFallback(player, card);
            renderHands();
        }

        if (gameState.playedCards.length === 4) {
            setTimeout(() => {
                evaluateTrick();
            }, 500);
        } else {
            const nextPlayer = getNextPlayer(gameState.currentPlayer);
            gameState.currentPlayer = nextPlayer;

            if (gameState.currentPlayer !== 'south') {
                setTimeout(() => {
                    aiPlayCard();
                }, 1000);
            } else {
                gameState.playerHasPlayedThisTrick = false;
                updatePlayableCards();
            }
        }

        updateStatus();
    }
    
    function animateCardToCenter(startRect, card, player) {
        const playedEl = $(`#played-${player}`);
        const targetRect = playedEl[0].getBoundingClientRect();

        const cardDims = getCardDimensions();
        const targetWidth = cardDims.width;
        const targetHeight = cardDims.height;

        const targetLeft = targetRect.left + (targetRect.width - targetWidth) / 2;
        const targetTop = targetRect.top + (targetRect.height - targetHeight) / 2;

        const flyingCard = createCardElement(card);
        flyingCard.addClass('flying-to-center');
        flyingCard.css({
            position: 'fixed',
            left: startRect.left,
            top: startRect.top,
            width: startRect.width + 'px',
            height: startRect.height + 'px',
            zIndex: 10000,
            transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            transformOrigin: 'center center',
            transform: 'none',
            margin: '0',
            padding: '5px',
            boxSizing: 'border-box',
            pointerEvents: 'none'
        });

        $('body').append(flyingCard);

        requestAnimationFrame(() => {
            requestAnimationFrame(() => {
                flyingCard.css({
                    left: targetLeft + 'px',
                    top: targetTop + 'px',
                    width: targetWidth + 'px',
                    height: targetHeight + 'px'
                });
            });
        });

        let animationCompleted = false;

        const completeAnimation = () => {
            if (!animationCompleted) {
                animationCompleted = true;
                flyingCard.remove();
                playedEl.empty().append(createCardElement(card));
            }
        };

        flyingCard.on('transitionend', completeAnimation);
        setTimeout(completeAnimation, 450);
    }

    function renderPlayedCardFallback(player, card) {
        const playedEl = $(`#played-${player}`);
        const sourceEl = $(`#player-${player} .player-hand`);
        const sourceRect = sourceEl[0].getBoundingClientRect();
        const targetRect = playedEl[0].getBoundingClientRect();

        const cardDims = getCardDimensions();
        const targetWidth = cardDims.width;
        const targetHeight = cardDims.height;

        const startLeft = sourceRect.left + sourceRect.width / 2 - targetWidth / 2;
        const startTop = sourceRect.top + sourceRect.height / 2 - targetHeight / 2;
        const targetLeft = targetRect.left + (targetRect.width - targetWidth) / 2;
        const targetTop = targetRect.top + (targetRect.height - targetHeight) / 2;

        const cardEl = createCardElement(card);
        cardEl.addClass('flying-to-center');
        cardEl.css({
            position: 'fixed',
            left: startLeft + 'px',
            top: startTop + 'px',
            width: targetWidth + 'px',
            height: targetHeight + 'px',
            zIndex: 10000,
            transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            pointerEvents: 'none'
        });

        $('body').append(cardEl);

        requestAnimationFrame(() => {
            requestAnimationFrame(() => {
                cardEl.css({
                    left: targetLeft + 'px',
                    top: targetTop + 'px'
                });
            });
        });

        let animationCompleted = false;

        const completeAnimation = () => {
            if (!animationCompleted) {
                animationCompleted = true;
                cardEl.remove();
                playedEl.empty().append(createCardElement(card));
            }
        };

        cardEl.on('transitionend', completeAnimation);
        setTimeout(completeAnimation, 450);
    }



    function getNextPlayer(currentPlayer) {
        const index = gameState.players.indexOf(currentPlayer);
        return gameState.players[(index + 1) % 4];
    }
    
    function evaluateTrick() {
        const leadSuit = gameState.playedCards[0].suit;
        let winner = gameState.leadPlayer;
        let highestRank = getRankValue(gameState.trickCards[winner].rank);
        let highestSpade = null;
        let highestSpadeRank = -1;
        
        // First, find the highest spade if any were played
        gameState.players.forEach(player => {
            const card = gameState.trickCards[player];
            if (card && card.suit === 'spades') {
                const rankValue = getRankValue(card.rank);
                if (rankValue > highestSpadeRank) {
                    highestSpadeRank = rankValue;
                    highestSpade = player;
                }
            }
        });
        
        // If spades were played, spades win
        if (highestSpade !== null) {
            winner = highestSpade;
        } else {
            // Otherwise, highest card of lead suit wins
            gameState.players.forEach(player => {
                const card = gameState.trickCards[player];
                if (card && card.suit === leadSuit) {
                    const rankValue = getRankValue(card.rank);
                    if (rankValue > highestRank) {
                        highestRank = rankValue;
                        winner = player;
                    }
                }
            });
        }
        
        gameState.trickWinner = winner;
        showTrickWinner(winner);
        
        setTimeout(() => {
            collectTrick(winner);
        }, 2000);
    }
    
    function getRankValue(rank) {
        const values = { '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, 
                        '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14 };
        return values[rank];
    }
    
    function showTrickWinner(winner) {
        const winnerEl = $('#trickWinner');
        const playerNames = { south: 'You', west: 'Alice', north: 'Bob', east: 'Charlie' };
        winnerEl.text(playerNames[winner]).addClass('show');
    }
    
    function collectTrick(winner) {
        // In Spades, each trick is worth 1 point to the winner
        gameState.tricksWon[winner]++;

        // Update tricks display
        $(`#tricks-${winner}`).text(gameState.tricksWon[winner]);
        
        // Update current round scores (for display purposes)
        gameState.currentRoundScores[winner] += 1;

        animateCardsToWinner(winner, () => {
            $('.played-card').empty();
            $('#trickWinner').removeClass('show');

            gameState.playedCards = [];
            gameState.trickCards = {};
            gameState.currentTrick++;
            gameState.leadPlayer = winner;
            gameState.currentPlayer = winner;
            gameState.playerHasPlayedThisTrick = false;
            gameState.aiThinking = false;

            updateScores();
            updateStatus();

            if (gameState.hands.south.length === 0) {
                endRound();
            } else {
                if (gameState.currentPlayer !== 'south') {
                    setTimeout(() => {
                        aiPlayCard();
                    }, 1000);
                } else {
                    updatePlayableCards();
                }
            }
        });
    }

    function animateCardsToWinner(winner, callback) {
        const winnerEl = domCache.players[winner]?.hand.parent() || $(`#player-${winner}`);
        const winnerRect = winnerEl[0].getBoundingClientRect();

        const playedCards = $('.played-card').find('.card');
        const totalAnimations = playedCards.length;

        if (totalAnimations === 0) {
            callback();
            return;
        }

        const animationPromises = [];

        playedCards.each(function(index) {
            const cardEl = $(this);
            const cardRect = cardEl[0].getBoundingClientRect();

            const flyingCard = cardEl.clone();
            flyingCard.addClass('flying-to-player');
            flyingCard.css({
                position: 'fixed',
                left: cardRect.left,
                top: cardRect.top,
                width: cardRect.width,
                height: cardRect.height,
                zIndex: 9999 - index,
                transition: 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                willChange: 'transform, opacity'
            });

            $('body').append(flyingCard);
            cardEl.css('opacity', '0');

            const animationPromise = new Promise(resolve => {
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        flyingCard.css({
                            left: winnerRect.left + winnerRect.width / 2 - 15,
                            top: winnerRect.top + winnerRect.height / 2 - 21,
                            width: '30px',
                            height: '42px',
                            opacity: '0'
                        });
                    }, index * 50 + 25);

                    setTimeout(() => {
                        flyingCard.remove();
                        resolve();
                    }, index * 50 + 650);
                });
            });

            animationPromises.push(animationPromise);
        });

        Promise.all(animationPromises).then(callback);
    }

    function aiBid(player) {
        if (player === 'south') {
            return;
        }

        const hand = gameState.hands[player];
        const aiConfig = gameState.aiMemory[player];
        
        // Simple AI bidding strategy
        const spadesCount = hand.filter(card => card.suit === 'spades').length;
        const highCards = hand.filter(card => getRankValue(card.rank) >= 10).length;
        const aces = hand.filter(card => card.rank === 'A').length;
        
        let bid = Math.floor((spadesCount + highCards + aces) / 3);
        bid = Math.max(0, Math.min(13, bid));
        
        // Add some randomness based on AI personality
        if (aiConfig.personality === 'aggressive') {
            bid = Math.min(13, bid + Math.floor(Math.random() * 2));
        } else if (aiConfig.personality === 'conservative') {
            bid = Math.max(0, bid - Math.floor(Math.random() * 2));
        }
        
        gameState.bids[player] = bid;
        $(`#bid-${player}`).text(bid);
        
        // Simulate thinking time
        setTimeout(() => {
            if (gameState.biddingPhase && gameState.bids[player] === -1) {
                aiBid(player);
            }
        }, 500 + Math.random() * 1000);
    }

    function aiPlayCard() {
        const player = gameState.currentPlayer;

        if (player === 'south') {
            console.error('aiPlayCard called for human player!');
            return;
        }

        if (gameState.aiThinking) {
            return;
        }

        if (gameState.playedCards.length >= 4) {
            return;
        }

        if (gameState.trickCards[player]) {
            return;
        }

        gameState.aiThinking = true;

        const hand = gameState.hands[player];
        const validCards = hand.filter(card => isValidPlay(card, player));
        const aiConfig = gameState.aiMemory[player];

        if (validCards.length === 0) {
            console.error(`No valid cards for AI ${player}!`);
            gameState.aiThinking = false;
            return;
        }

        const gameAnalysis = analyzeGameState(player);
        const handAnalysis = analyzeHand(hand);

        let selectedCard;
        if (gameState.playedCards.length === 0) {
            selectedCard = aiSelectLeadCardAdvanced(validCards, gameAnalysis, handAnalysis, aiConfig);
        } else {
            selectedCard = aiSelectFollowCardAdvanced(validCards, gameAnalysis, handAnalysis, aiConfig);
        }

        if (!selectedCard) {
            selectedCard = validCards[0];
        }

        const thinkingTime = aiConfig.difficulty === 'advanced' ?
            800 + Math.random() * 400 :
            500 + Math.random() * 300;

        setTimeout(() => {
            gameState.aiThinking = false;
            if (gameState.currentPlayer === player && !gameState.trickCards[player]) {
                playCard(player, selectedCard);
            }
        }, thinkingTime);
    }



    function aiSelectLeadCardAdvanced(validCards, gameAnalysis, handAnalysis, aiConfig) {
        // In Spades, lead strategy is different
        const spadesCards = validCards.filter(card => card.suit === 'spades');
        const nonSpadesCards = validCards.filter(card => card.suit !== 'spades');
        
        // If we have spades and want to be aggressive, lead with a spade
        if (spadesCards.length > 0 && aiConfig.personality === 'aggressive') {
            return spadesCards.reduce((highest, card) =>
                getRankValue(card.rank) > getRankValue(highest.rank) ? card : highest
            );
        }
        
        // Otherwise, lead with a high non-spade card
        if (nonSpadesCards.length > 0) {
            return nonSpadesCards.reduce((highest, card) =>
                getRankValue(card.rank) > getRankValue(highest.rank) ? card : highest
            );
        }
        
        // If only spades available, play the lowest spade
        return spadesCards.reduce((lowest, card) =>
            getRankValue(card.rank) < getRankValue(lowest.rank) ? card : lowest
        );
    }

    function attemptShootingMoonLead(validCards) {
        const highCards = validCards.filter(card => getRankValue(card.rank) >= 11);
        if (highCards.length > 0) {
            return highCards.reduce((highest, card) =>
                getRankValue(card.rank) > getRankValue(highest.rank) ? card : highest
            );
        }
        return validCards[0];
    }

    function blockShootingMoonLead(validCards) {
        const safeCards = validCards.filter(card =>
            card.suit !== 'hearts' && !(card.suit === 'spades' && card.rank === 'Q')
        );

        if (safeCards.length > 0) {
            const lowCards = safeCards.filter(card => getRankValue(card.rank) <= 7);
            if (lowCards.length > 0) {
                return lowCards[Math.floor(Math.random() * lowCards.length)];
            }
            return safeCards[0];
        }

        return validCards[0];
    }

    function endGameLeadStrategy(validCards, gameAnalysis) {
        if (gameAnalysis.isLeading) {
            const safeCards = validCards.filter(card =>
                card.suit !== 'hearts' && !(card.suit === 'spades' && card.rank === 'Q')
            );
            if (safeCards.length > 0) {
                return safeCards.reduce((lowest, card) =>
                    getRankValue(card.rank) < getRankValue(lowest.rank) ? card : lowest
                );
            }
        }

        if (gameAnalysis.isLosing) {
            const riskCards = validCards.filter(card =>
                card.suit === 'hearts' || (card.suit === 'spades' && ['Q', 'K', 'A'].includes(card.rank))
            );
            if (riskCards.length > 0) {
                return riskCards[Math.floor(Math.random() * riskCards.length)];
            }
        }

        return validCards[0];
    }

    function determineLeadStrategy(gameAnalysis, handAnalysis, aiConfig) {
        if (gameAnalysis.isLosing && gameAnalysis.isEndGame) {
            return 'aggressive';
        }

        if (gameAnalysis.isLeading && gameAnalysis.tricksRemaining < 5) {
            return 'conservative';
        }

        if (handAnalysis.hasQueenOfSpades && !gameAnalysis.queenOfSpadesPlayed) {
            return 'control';
        }

        return aiConfig.personality === 'aggressive' ? 'aggressive' :
               aiConfig.personality === 'conservative' ? 'conservative' : 'balanced';
    }

    function aggressiveLeadStrategy(validCards) {
        const highRiskCards = validCards.filter(card =>
            card.suit === 'spades' && ['K', 'A'].includes(card.rank)
        );
        if (highRiskCards.length > 0) {
            return highRiskCards[0];
        }

        const mediumCards = validCards.filter(card =>
            getRankValue(card.rank) >= 8 && card.suit !== 'hearts'
        );
        if (mediumCards.length > 0) {
            return mediumCards[0];
        }

        return validCards[0];
    }

    function conservativeLeadStrategy(validCards) {
        const safeCards = validCards.filter(card =>
            card.suit !== 'hearts' && !(card.suit === 'spades' && card.rank === 'Q')
        );

        if (safeCards.length > 0) {
            const lowCards = safeCards.filter(card => getRankValue(card.rank) <= 7);
            if (lowCards.length > 0) {
                return lowCards.reduce((lowest, card) =>
                    getRankValue(card.rank) < getRankValue(lowest.rank) ? card : lowest
                );
            }
            return safeCards[0];
        }

        return validCards[0];
    }

    function controlLeadStrategy(validCards, handAnalysis) {
        if (handAnalysis.hasQueenOfSpades && handAnalysis.highSpades.length === 0) {
            const lowSpades = validCards.filter(card =>
                card.suit === 'spades' && getRankValue(card.rank) <= 10
            );
            if (lowSpades.length > 0) {
                return lowSpades[0];
            }
        }

        const neutralCards = validCards.filter(card =>
            (card.suit === 'clubs' || card.suit === 'diamonds') &&
            getRankValue(card.rank) >= 7 && getRankValue(card.rank) <= 10
        );

        if (neutralCards.length > 0) {
            return neutralCards[0];
        }

        return validCards[0];
    }

    function balancedLeadStrategy(validCards) {
        const preferredSuits = ['clubs', 'diamonds'];

        for (const suit of preferredSuits) {
            const suitCards = validCards.filter(c => c.suit === suit);
            if (suitCards.length > 0) {
                const midRangeCards = suitCards.filter(card =>
                    getRankValue(card.rank) >= 6 && getRankValue(card.rank) <= 9
                );
                if (midRangeCards.length > 0) {
                    return midRangeCards[0];
                }
                return suitCards[0];
            }
        }

        return validCards[0];
    }

    function aiSelectFollowCardAdvanced(validCards, gameAnalysis, handAnalysis, aiConfig) {
        const leadSuit = gameState.playedCards[0].suit;
        const suitCards = validCards.filter(c => c.suit === leadSuit);

        if (suitCards.length > 0) {
            return followSuitStrategy(suitCards, gameAnalysis, handAnalysis, aiConfig);
        } else {
            return discardStrategy(validCards, gameAnalysis, handAnalysis);
        }
    }

    function followSuitStrategy(suitCards, gameAnalysis, handAnalysis, aiConfig) {
        const currentWinningCard = getCurrentWinningCard();
        const winningCards = suitCards.filter(c => getRankValue(c.rank) > getRankValue(currentWinningCard.rank));
        
        // In Spades, try to win if we can
        if (winningCards.length > 0) {
            if (aiConfig.personality === 'aggressive') {
                return winningCards.reduce((highest, card) =>
                    getRankValue(card.rank) > getRankValue(highest.rank) ? card : highest
                );
            } else {
                // Conservative: play the lowest winning card
                return winningCards.reduce((lowest, card) =>
                    getRankValue(card.rank) < getRankValue(lowest.rank) ? card : lowest
                );
            }
        }
        
        // If we can't win, play the lowest card
        return suitCards.reduce((lowest, card) =>
            getRankValue(card.rank) < getRankValue(lowest.rank) ? card : lowest
        );
    }

    function getCurrentWinningCard() {
        const leadSuit = gameState.playedCards[0].suit;
        
        // Check if any spades were played
        const spadesPlayed = gameState.playedCards.filter(c => c.suit === 'spades');
        if (spadesPlayed.length > 0) {
            // Spades win, return the highest spade
            return spadesPlayed.reduce((highest, card) =>
                getRankValue(card.rank) > getRankValue(highest.rank) ? card : highest
            );
        }
        
        // Otherwise, highest card of lead suit wins
        return gameState.playedCards.filter(c => c.suit === leadSuit)
            .reduce((highest, card) =>
                getRankValue(card.rank) > getRankValue(highest.rank) ? card : highest
            );
    }

    function discardStrategy(validCards, gameAnalysis, handAnalysis) {
        // In Spades, discard strategy is simpler
        // Prefer to discard low cards of non-spades suits
        const nonSpadesCards = validCards.filter(card => card.suit !== 'spades');
        
        if (nonSpadesCards.length > 0) {
            // Play the lowest non-spade card
            return nonSpadesCards.reduce((lowest, card) =>
                getRankValue(card.rank) < getRankValue(lowest.rank) ? card : lowest
            );
        }
        
        // If only spades available, play the lowest spade
        return validCards.reduce((lowest, card) =>
            getRankValue(card.rank) < getRankValue(lowest.rank) ? card : lowest
        );
    }

    function calculateTrickPoints(playedCards) {
        // In Spades, each trick is worth 1 point
        return playedCards.length;
    }

    function updatePlayableCards() {
        if (gameState.currentPlayer !== 'south' || gameState.passingPhase || gameState.biddingPhase) {
            return;
        }

        const selectableCards = domCache.players.south?.hand.find('.card.selectable') || $('.card.selectable');
        selectableCards.removeClass('playable unplayable');

        const cardMap = new Map();
        selectableCards.each(function() {
            const cardData = $(this).data('card');
            if (cardData) {
                const key = `${cardData.suit}-${cardData.rank}`;
                cardMap.set(key, $(this));
            }
        });

        gameState.hands.south.forEach(card => {
            const key = `${card.suit}-${card.rank}`;
            const cardEl = cardMap.get(key);

            if (cardEl) {
                if (isValidPlay(card)) {
                    cardEl.addClass('playable');
                } else {
                    cardEl.addClass('unplayable');
                }
            }
        });
    }

    function updateScores() {
        requestAnimationFrame(() => {
            gameState.players.forEach(player => {
                const scoreEl = domCache.players[player]?.score || $(`#score-${player}`);
                scoreEl.text(gameState.currentRoundScores[player]);
            });
        });
    }

    const updateStatus = debounce(function() {
        const playerNames = { south: 'You', west: 'Alice', north: 'Bob', east: 'Charlie' };
        let message = '';

        const statusEl = domCache.statusMessage || $('#statusMessage');
        const roundEl = domCache.currentRound || $('#currentRound');
        const trickEl = domCache.currentTrick || $('#currentTrick');

        if (gameState.biddingPhase) {
            message = 'Make your bid - Click on a number to bid how many tricks you think you can win';
            statusEl.css('cursor', 'default').off('click');
        } else if (gameState.passingPhase) {
            message = 'Select 3 cards to pass - Click here if modal is closed';
            statusEl.css('cursor', 'pointer').off('click').on('click', function() {
                if (gameState.passingPhase && $('#passingModal').hasClass('hidden')) {
                    showPassingModal();
                }
            });
        } else {
            statusEl.css('cursor', 'default').off('click');
            if (gameState.currentPlayer === 'south') {
                message = 'Your turn - click a card to play (Spades are trump)';
            } else {
                message = `${playerNames[gameState.currentPlayer]}'s turn`;
            }
        }

        requestAnimationFrame(() => {
            statusEl.text(message);
            roundEl.text(gameState.currentRound);
            trickEl.text(gameState.currentTrick);
        });
    }, 50);

    function startBiddingPhase() {
        gameState.biddingPhase = true;
        gameState.selectedBid = null;
        gameState.bids = { south: -1, west: -1, north: -1, east: -1 };
        gameState.tricksWon = { south: 0, west: 0, north: 0, east: 0 };

        renderHands();
        showBiddingInterface();
        updateStatus();

        // Start AI bidding
        setTimeout(() => {
            aiBid('west');
        }, 1000);
        setTimeout(() => {
            aiBid('north');
        }, 2000);
        setTimeout(() => {
            aiBid('east');
        }, 3000);
    }

    function showBiddingInterface() {
        $('#biddingSlots').show();
        $('#statusMessage').html(`
            <strong>Make Your Bid</strong><br>
            <small>Click on a number to bid how many tricks you think you can win in Spades</small>
        `);

        $('.bidding-slot').removeClass('selected');
        $('#confirmBidBtn').prop('disabled', true);
    }

    function showPassingInterface() {
        const directionText = {
            left: 'Pass to Alice (left)',
            right: 'Pass to Charlie (right)',
            across: 'Pass to Bob (across)'
        };

        $('#passingSlots').show();
        $('#statusMessage').html(`
            <strong>${directionText[gameState.passingDirection]}</strong><br>
            <small>Click on 3 cards from your hand to select them for passing in Spades</small>
        `);

        $('.passing-slot').empty().removeClass('filled');
        $('.passing-slot').each(function() {
            $(this).html('<div class="slot-number">' + $(this).data('slot') + '</div>');
        });

        $('#passCardsBtn').prop('disabled', true);
    }

    function updateBiddingInterface() {
        $('.bidding-slot').each(function() {
            const slot = $(this);
            const bidValue = parseInt(slot.data('bid'));
            
            if (gameState.selectedBid === bidValue) {
                slot.addClass('selected');
            } else {
                slot.removeClass('selected');
            }
        });

        $('#confirmBidBtn').prop('disabled', gameState.selectedBid === null);
    }

    function updatePassingInterface() {
        $('.passing-slot').each(function(index) {
            const slot = $(this);
            const slotNumber = index + 1;

            if (index < gameState.selectedCards.length) {
                const card = gameState.selectedCards[index];
                const cardEl = createCardElement(card);

                const cardDims = getPassingSlotCardDimensions();
                const cardWidth = cardDims.width + 'px';
                const cardHeight = cardDims.height + 'px';
                const fontSize = Math.max(8, Math.floor(cardDims.width * 0.2)) + 'px';

                cardEl.css({
                    position: 'relative',
                    width: cardWidth,
                    height: cardHeight,
                    fontSize: fontSize,
                    cursor: 'pointer'
                });

                cardEl.on('click', () => {
                    gameState.selectedCards.splice(index, 1);
                    gameState.hands.south.push(card);
                    sortHand(gameState.hands.south);
                    updatePassingInterface();
                    renderHands();
                });

                slot.addClass('filled').empty().append(cardEl);
            } else {
                slot.removeClass('filled').empty().html('<div class="slot-number">' + slotNumber + '</div>');
            }
        });

        $('#passCardsBtn').prop('disabled', gameState.selectedCards.length !== 3);
    }

    function confirmBid() {
        if (gameState.selectedBid === null) {
            return;
        }

        gameState.bids.south = gameState.selectedBid;
        $('#biddingSlots').hide();
        gameState.biddingPhase = false;

        // Update bid display
        $('#bid-south').text(gameState.selectedBid);

        $('#statusMessage').text('Bid confirmed! Starting round...');

        // Wait for all AI to finish bidding
        const checkAllBids = () => {
            const allBidsComplete = gameState.players.every(player => gameState.bids[player] !== -1);
            if (allBidsComplete) {
                setTimeout(() => {
                    startNewRound();
                }, 1000);
            } else {
                setTimeout(checkAllBids, 500);
            }
        };
        checkAllBids();
    }

    function confirmPass() {
        if (gameState.selectedCards.length !== 3) {
            return;
        }

        executeSimultaneousPassing();

        $('#passingSlots').hide();
        gameState.passingPhase = false;
        gameState.selectedCards = [];

        sortHand(gameState.hands.south);
        renderHands();

        $('#statusMessage').text('Cards passed! Starting round...');

        setTimeout(() => {
            startNewRound();
        }, 1000);
    }

    function executeSimultaneousPassing() {
        const allPassingDecisions = {};
        const originalHands = {};

        gameState.players.forEach(player => {
            originalHands[player] = [...gameState.hands[player]];
        });

        allPassingDecisions['south'] = {
            cardsToPass: [...gameState.selectedCards],
            targetPlayer: getPassingTarget('south')
        };

        gameState.players.filter(p => p !== 'south').forEach((player, index) => {
            const originalHand = originalHands[player];
            const cardsToPass = selectCardsToPass(originalHand, index);

            allPassingDecisions[player] = {
                cardsToPass: cardsToPass,
                targetPlayer: getPassingTarget(player)
            };
        });

        gameState.players.forEach(player => {
            const decision = allPassingDecisions[player];
            const hand = gameState.hands[player];

            decision.cardsToPass.forEach(card => {
                const cardIndex = hand.findIndex(c => c.suit === card.suit && c.rank === card.rank);
                if (cardIndex !== -1) {
                    hand.splice(cardIndex, 1);
                }
            });
        });

        gameState.players.forEach(player => {
            const decision = allPassingDecisions[player];
            gameState.hands[decision.targetPlayer].push(...decision.cardsToPass);
            sortHand(gameState.hands[decision.targetPlayer]);
        });
    }

    function getPassingTarget(player) {
        const playerIndex = gameState.players.indexOf(player);

        switch (gameState.passingDirection) {
            case 'left':
                return gameState.players[(playerIndex + 1) % 4];
            case 'right':
                return gameState.players[(playerIndex + 3) % 4];
            case 'across':
                return gameState.players[(playerIndex + 2) % 4];
            default:
                return player;
        }
    }

    function calculateCardDanger(card) {
        let danger = 0;

        if (card.suit === 'spades' && card.rank === 'Q') {
            danger = 100;
        } else if (card.suit === 'hearts') {
            danger = 20 + getRankValue(card.rank);
        } else if (card.suit === 'spades') {
            if (['K', 'A'].includes(card.rank)) {
                danger = 15 + getRankValue(card.rank);
            } else {
                danger = getRankValue(card.rank);
            }
        } else {
            danger = getRankValue(card.rank) * 0.5;
        }

        return danger;
    }



    function selectCardsToPass(hand, playerIndex) {
        const analysis = analyzeHand(hand);
        const cardsToPass = [];
        const availableCards = [...hand];

        const aiPersonality = ['aggressive', 'conservative', 'balanced'][playerIndex % 3];
        const randomFactor = 0.1 + Math.random() * 0.2;

        const cardScores = availableCards.map(card => ({
            card: card,
            danger: calculateCardDanger(card),
            priority: 0
        }));

        cardScores.forEach(item => {
            const card = item.card;
            let priority = item.danger;

            if (card.suit === 'spades' && card.rank === 'Q') {
                let baseQueenPriority = 85;

                if (analysis.spades.length <= 3) {
                    baseQueenPriority += 20;
                } else if (analysis.spades.length >= 7) {
                    baseQueenPriority -= 15;
                }

                if (analysis.hasQueenOfSpades && analysis.highSpades.length >= 2) {
                    baseQueenPriority -= 20;
                }

                if (aiPersonality === 'conservative') {
                    baseQueenPriority += 10;
                } else if (aiPersonality === 'aggressive') {
                    baseQueenPriority -= 5;
                }

                priority = baseQueenPriority + Math.random() * 20;
            } else if (card.suit === 'spades' && ['K', 'A'].includes(card.rank)) {
                if (analysis.hasQueenOfSpades) {
                    priority *= 0.3;
                } else {
                    priority *= 1.5;
                }
            } else if (card.suit === 'hearts') {
                if (analysis.hearts.length <= 3) {
                    priority *= 1.8;
                } else if (analysis.hearts.length >= 8) {
                    priority *= 0.6;
                }

                if (getRankValue(card.rank) >= 12) {
                    priority *= 1.4;
                }
            } else if (analysis.voidSuits.length > 0 && !analysis.voidSuits.includes(card.suit)) {
                if (analysis[card.suit].length === 1) {
                    priority *= 1.3;
                }
            }

            if (aiPersonality === 'aggressive') {
                if (card.suit === 'hearts' || (card.suit === 'spades' && ['Q', 'K', 'A'].includes(card.rank))) {
                    priority *= 1.2;
                }
            } else if (aiPersonality === 'conservative') {
                if (card.suit === 'clubs' || card.suit === 'diamonds') {
                    priority *= 0.8;
                }
            }

            priority *= (1 + (Math.random() - 0.5) * randomFactor);
            item.priority = priority;
        });

        cardScores.sort((a, b) => b.priority - a.priority);

        for (let i = 0; i < 3 && i < cardScores.length; i++) {
            cardsToPass.push(cardScores[i].card);
        }

        return cardsToPass;
    }



    function startNewRound() {
        gameState.currentTrick = 1;
        gameState.playedCards = [];
        gameState.trickCards = {};
        gameState.spadesBroken = false;
        gameState.playerHasPlayedThisTrick = false;
        gameState.aiThinking = false;

        const twoOfClubs = gameState.hands.south.find(card =>
            card.suit === 'clubs' && card.rank === '2'
        );
        if (twoOfClubs) {
            gameState.leadPlayer = 'south';
            gameState.currentPlayer = 'south';
        } else {
            for (let player of gameState.players) {
                const hasTwo = gameState.hands[player].find(card =>
                    card.suit === 'clubs' && card.rank === '2'
                );
                if (hasTwo) {
                    gameState.leadPlayer = player;
                    gameState.currentPlayer = player;
                    break;
                }
            }
        }

        renderHands();
        updateStatus();

        if (gameState.currentPlayer !== 'south') {
            setTimeout(() => {
                aiPlayCard();
            }, 1000);
        } else {
            updatePlayableCards();
        }
    }



    function endRound() {
        if (!gameState.previousRoundScores) {
            gameState.previousRoundScores = { south: 0, west: 0, north: 0, east: 0 };
        }

        // Calculate Spades scoring
        const roundPoints = {};
        gameState.players.forEach(player => {
            const bid = gameState.bids[player];
            const tricks = gameState.tricksWon[player];
            
            if (tricks >= bid) {
                // Met or exceeded bid: 10 points per trick + 1 point per trick over bid
                roundPoints[player] = (bid * 10) + (tricks - bid);
            } else {
                // Failed to meet bid: -10 points per trick short
                roundPoints[player] = -(bid * 10);
            }
        });

        gameState.players.forEach(player => {
            gameState.scores[player] += roundPoints[player];
        });

        gameState.scoreHistory.push({
            round: gameState.currentRound,
            scores: { ...roundPoints },
            bids: { ...gameState.bids },
            tricks: { ...gameState.tricksWon }
        });

        gameState.previousRoundScores = { ...gameState.scores };

        gameState.currentRoundScores = { south: 0, west: 0, north: 0, east: 0 };
        updateScores();

        const gameEnded = Object.values(gameState.scores).some(score => score >= 500);

        if (gameEnded) {
            gameState.gameOver = true;
            clearGameState();
            endGame();
            return;
        }

        showDetailedScoreboard(() => {
            gameState.currentRound++;
            saveGameState();
            dealCards();
            setTimeout(() => {
                startBiddingPhase();
            }, 500);
        }, false, false);
    }

    function showDetailedScoreboard(callback, isGameEnd = false, showNewGameOption = false) {
        $('#scoreboardTitle').text(isGameEnd ? 'Final Spades Game Results' : 'Detailed Spades Game Results');

        showGameSummary(isGameEnd);
        showScoreboardTable();

        const modal = domCache.scoreboardModal || $('#scoreboardModal');
        modal.removeClass('hidden');

        const continueBtn = domCache.continueBtn || $('#continueBtn');
        const newGameBtn = domCache.scoreboardNewGameBtn || $('#scoreboardNewGameBtn');

        if (isGameEnd) {
            continueBtn.hide();
            newGameBtn.text('New Game').show();
        } else if (showNewGameOption) {
            continueBtn.show();
            newGameBtn.text('Start New Game').show();
        } else {
            continueBtn.show();
            newGameBtn.hide();
        }

        continueBtn.off('click').on('click', function() {
            modal.addClass('hidden');
            if (callback) callback();
        });

        newGameBtn.off('click').on('click', function() {
            modal.addClass('hidden');
            clearGameState();
            startFreshGame();
        });




    }

    function showGameSummary(isGameEnd = false) {
        const gameSummary = domCache.gameSummary || $('#gameSummary');
        gameSummary.empty();

        const playerNames = { south: 'You', west: 'Alice', north: 'Bob', east: 'Charlie' };

        if (isGameEnd) {
            showFinalRankings(gameSummary, playerNames);
        } else {
            showRoundSummary(gameSummary, playerNames);
        }
    }

    function showFinalRankings(gameSummary, playerNames) {
        const summaryTitle = $('<h3>').text('Final Spades Rankings');
        gameSummary.append(summaryTitle);

        const rankingsTable = $('<div>').addClass('final-rankings-table');

        const headerRow = $('<div>').addClass('ranking-row ranking-header');
        headerRow.html(`
            <div class="ranking-place">Place</div>
            <div class="ranking-name">Name</div>
            <div class="ranking-score">Score</div>
        `);
        rankingsTable.append(headerRow);

        const sortedPlayers = gameState.players.slice().sort((a, b) =>
            gameState.scores[a] - gameState.scores[b]
        );

        const rankLabels = ['1st', '2nd', '3rd', '4th'];

        sortedPlayers.forEach((player, index) => {
            const row = $('<div>').addClass('ranking-row');
            const isWinner = index === 0;

            if (isWinner) {
                row.addClass('winner-row');
            }

            row.html(`
                <div class="ranking-place ${isWinner ? 'winner-place' : ''}">${rankLabels[index]}</div>
                <div class="ranking-name ${isWinner ? 'winner-name' : ''}">${playerNames[player]}</div>
                <div class="ranking-score ${isWinner ? 'winner-score' : ''}">${gameState.scores[player]}</div>
            `);
            rankingsTable.append(row);
        });

        gameSummary.append(rankingsTable);
    }

    function showRoundSummary(gameSummary, playerNames) {
        const currentLeader = gameState.players.reduce((leader, player) =>
            gameState.scores[player] < gameState.scores[leader] ? player : leader
        );

        const summaryTitle = $('<h3>').text(`Round ${gameState.currentRound} Complete`);
        gameSummary.append(summaryTitle);

        const statsDiv = $('<div>').addClass('summary-stats');

        const totalRounds = $('<div>').addClass('summary-stat');
        totalRounds.html(`
            <div class="stat-label">Rounds Played</div>
            <div class="stat-value">${gameState.currentRound}</div>
        `);
        statsDiv.append(totalRounds);

        const currentLeaderStat = $('<div>').addClass('summary-stat');
        currentLeaderStat.html(`
            <div class="stat-label">Current Leader</div>
            <div class="stat-value">${playerNames[currentLeader]}</div>
        `);
        statsDiv.append(currentLeaderStat);

        const leaderScore = $('<div>').addClass('summary-stat');
        leaderScore.html(`
            <div class="stat-label">Leader Score</div>
            <div class="stat-value">${gameState.scores[currentLeader]}</div>
        `);
        statsDiv.append(leaderScore);

        // Add Spades-specific stats
        const roundResults = $('<div>').addClass('round-results');
        roundResults.html('<h4>Round Results</h4>');
        
        const resultsTable = $('<div>').addClass('results-table');
        const headerRow = $('<div>').addClass('results-row header');
        headerRow.html(`
            <div class="player-name">Player</div>
            <div class="bid">Bid</div>
            <div class="tricks">Tricks</div>
            <div class="points">Points</div>
        `);
        resultsTable.append(headerRow);
        
        gameState.players.forEach(player => {
            const row = $('<div>').addClass('results-row');
            const bid = gameState.bids[player];
            const tricks = gameState.tricksWon[player];
            const points = gameState.scoreHistory[gameState.scoreHistory.length - 1].scores[player];
            
            row.html(`
                <div class="player-name">${playerNames[player]}</div>
                <div class="bid">${bid}</div>
                <div class="tricks">${tricks}</div>
                <div class="points">${points > 0 ? '+' : ''}${points}</div>
            `);
            resultsTable.append(row);
        });
        
        roundResults.append(resultsTable);
        gameSummary.append(roundResults);

        const gameProgress = Math.min(Math.max(...Object.values(gameState.scores)) / 100 * 100, 100);
        const progressStat = $('<div>').addClass('summary-stat');
        progressStat.html(`
            <div class="stat-label">Game Progress</div>
            <div class="stat-value">${gameProgress.toFixed(0)}%</div>
        `);
        statsDiv.append(progressStat);

        gameSummary.append(statsDiv);
    }

    function showScoreboardTable() {
        const playerNames = { south: 'You', west: 'Alice', north: 'Bob', east: 'Charlie' };
        const scoreboardBody = domCache.scoreboardBody || $('#scoreboardBody');
        scoreboardBody.empty();

        const fragment = document.createDocumentFragment();

        const headerRow = $('<div>').addClass('scoreboard-row header-row');
        headerRow.append($('<div>').addClass('round-cell').text('Round'));
        gameState.players.forEach(player => {
            headerRow.append($('<div>').addClass('player-cell').text(playerNames[player]));
        });
        fragment.appendChild(headerRow[0]);

        const reversedHistory = gameState.scoreHistory.slice().reverse();

        reversedHistory.forEach((roundData) => {
            const row = $('<div>').addClass('scoreboard-row');
            row.append($('<div>').addClass('round-cell').text(roundData.round));

            gameState.players.forEach(player => {
                const cell = $('<div>').addClass('player-cell');
                const score = roundData.scores[player];
                const sign = score > 0 ? '+' : '';

                const totalAtThisRound = gameState.scoreHistory
                    .filter(round => round.round <= roundData.round)
                    .reduce((sum, round) => sum + round.scores[player], 0);

                cell.html(`<span class="round-score">${sign}${score}</span><span class="total-score">${totalAtThisRound}</span>`);
                row.append(cell);
            });
            fragment.appendChild(row[0]);
        });

        scoreboardBody.append(fragment);
    }

    function showGameResumeModal(savedData) {
        const playerNames = { south: 'You', west: 'Alice', north: 'Bob', east: 'Charlie' };

        $('#gameResumeTitle').text('Continue Spades Game');

        const resumeSummary = domCache.resumeSummary || $('#resumeSummary');
        resumeSummary.empty();

        const players = ['south', 'west', 'north', 'east'];
        const currentLeader = players.reduce((leader, player) =>
            savedData.scores[player] < savedData.scores[leader] ? player : leader
        );

        const gameInfo = $('<div>').addClass('summary-stats');

        const roundInfo = $('<div>').addClass('summary-stat');
        roundInfo.html(`
            <div class="stat-label">Next Round</div>
            <div class="stat-value">${savedData.currentRound}</div>
        `);
        gameInfo.append(roundInfo);

        const completedRounds = $('<div>').addClass('summary-stat');
        completedRounds.html(`
            <div class="stat-label">Completed Rounds</div>
            <div class="stat-value">${savedData.scoreHistory.length}</div>
        `);
        gameInfo.append(completedRounds);

        const leaderInfo = $('<div>').addClass('summary-stat');
        leaderInfo.html(`
            <div class="stat-label">Current Leader</div>
            <div class="stat-value">${playerNames[currentLeader]}</div>
        `);
        gameInfo.append(leaderInfo);

        const scoreInfo = $('<div>').addClass('summary-stat');
        scoreInfo.html(`
            <div class="stat-label">Leader Score</div>
            <div class="stat-value">${savedData.scores[currentLeader]}</div>
        `);
        gameInfo.append(scoreInfo);

        resumeSummary.append(gameInfo);

        const resumeScoreboard = domCache.resumeScoreboard || $('#resumeScoreboard');
        resumeScoreboard.empty();

        if (savedData.scoreHistory && savedData.scoreHistory.length > 0) {
            const fragment = document.createDocumentFragment();

            const headerRow = $('<div>').addClass('scoreboard-row header-row');
            headerRow.append($('<div>').addClass('round-cell').text('Round'));
            players.forEach(player => {
                headerRow.append($('<div>').addClass('player-cell').text(playerNames[player]));
            });
            fragment.appendChild(headerRow[0]);

            const reversedHistory = savedData.scoreHistory.slice().reverse();

            reversedHistory.forEach((roundData) => {
                const row = $('<div>').addClass('scoreboard-row');
                row.append($('<div>').addClass('round-cell').text(roundData.round));

                players.forEach(player => {
                    const cell = $('<div>').addClass('player-cell');
                    const score = roundData.scores[player];
                    const sign = score > 0 ? '+' : '';

                    const totalAtThisRound = savedData.scoreHistory
                        .slice(0, roundData.round)
                        .reduce((sum, round) => sum + round.scores[player], 0);

                    cell.html(`<span class="round-score">${sign}${score}</span><span class="total-score">${totalAtThisRound}</span>`);
                    row.append(cell);
                });
                fragment.appendChild(row[0]);
            });

            resumeScoreboard.append(fragment);
        }

        const modal = domCache.gameResumeModal || $('#gameResumeModal');
        modal.removeClass('hidden');

        const resumeBtn = domCache.resumeGameBtn || $('#resumeGameBtn');
        const newGameBtn = domCache.startNewGameBtn || $('#startNewGameBtn');

        resumeBtn.off('click').on('click', function() {
            modal.addClass('hidden');
            restoreGameState(savedData);
        });

        newGameBtn.off('click').on('click', function() {
            modal.addClass('hidden');
            clearGameState();
            startFreshGame();
        });


    }

    function restoreGameState(savedData) {
        try {
            gameState.scoreHistory = [...savedData.scoreHistory];
            gameState.scores = { ...savedData.scores };
            gameState.currentRound = savedData.currentRound;

            gameState.currentRoundScores = { south: 0, west: 0, north: 0, east: 0 };
            gameState.previousRoundScores = { ...savedData.scores };

            updateScores();
            dealCards();
            setTimeout(() => {
                startBiddingPhase();
            }, 500);
        } catch (error) {
            console.error('Failed to restore game progress:', error);
            clearGameState();
            startFreshGame();
        }
    }

    function endGame() {
        showDetailedScoreboard(null, true);
    }



    function newGame() {

        const savedData = loadGameState();
        if (savedData) {
            showGameResumeModal(savedData);
            return;
        }

        startFreshGame();
    }

    function startFreshGame() {
        gameState = {
            players: ['south', 'west', 'north', 'east'],
            hands: { south: [], west: [], north: [], east: [] },
            scores: { south: 0, west: 0, north: 0, east: 0 },
            currentRoundScores: { south: 0, west: 0, north: 0, east: 0 },
            previousRoundScores: { south: 0, west: 0, north: 0, east: 0 },
            scoreHistory: [],
            currentRound: 1,
            currentTrick: 1,
            trickCards: {},
            currentPlayer: 'south',
            leadPlayer: 'south',
            spadesBroken: false,
            biddingPhase: false,
            bids: { south: -1, west: -1, north: -1, east: -1 },
            tricksWon: { south: 0, west: 0, north: 0, east: 0 },
            selectedBid: null,
            playedCards: [],
            gameOver: false,
            trickWinner: null,
            animatingCards: 0,
            playerHasPlayedThisTrick: false,
            playedCardsHistory: [],
            aiThinking: false,
            aiMemory: {
                west: { difficulty: 'intermediate', personality: 'balanced' },
                north: { difficulty: 'advanced', personality: 'aggressive' },
                east: { difficulty: 'advanced', personality: 'conservative' }
            }
        };

        $('.played-card').empty();
        $('#trickWinner').removeClass('show');
        $('.modal').addClass('hidden');
        $('.help-panel').addClass('hidden');

        updateScores();
        dealCards();
        setTimeout(() => {
            startBiddingPhase();
        }, 500);
    }

    $('#newGameBtn, #newGameFromModal').on('click', function() {
        if (!gameState.hasAutoFullscreened) {
            gameState.hasAutoFullscreened = true;
            autoFullscreen();
        }
        clearGameState();
        startFreshGame();
    });
    $('#passCardsBtn').on('click', confirmPass);
    $('#confirmBidBtn').on('click', confirmBid);
    $('#closeGameOver').on('click', () => $('#gameOverModal').addClass('hidden'));
    $('#helpBtn').on('click', () => $('#helpPanel').removeClass('hidden'));
    $('#closeHelpBtn, #closeHelpBtnBottom').on('click', () => $('#helpPanel').addClass('hidden'));
    $('#fullscreenBtn').on('click', toggleFullscreen);

    // Bidding slot click handlers
    $('.bidding-slot').on('click', function() {
        const bidValue = parseInt($(this).data('bid'));
        gameState.selectedBid = bidValue;
        updateBiddingInterface();
    });

    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('#helpPanel').addClass('hidden');
            $('#gameOverModal').addClass('hidden');
        }
        if (e.key === 'h' || e.key === 'H') {
            $('#helpPanel').removeClass('hidden');
        }
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            clearGameState();
            startFreshGame();
        }
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            toggleFullscreen();
        }
    });

    // Listen for fullscreen change events
    $(document).on('fullscreenchange webkitfullscreenchange mozfullscreenchange MSFullscreenChange', function() {
        updateFullscreenButton();
    });

    $('.card.selectable').on('mouseenter', function() {
        if (!gameState.passingPhase) {
            $(this).css('transform', 'translateY(-3px)');
        }
    });

    $('.card.selectable').on('mouseleave', function() {
        $(this).css('transform', '');
    });

    const handleResize = throttle(function() {
        const isMobile = window.innerWidth <= 768;
        const isPortrait = window.innerHeight > window.innerWidth;

        if (isMobile && isPortrait) {
            // 移动端竖屏：显示横屏提示
            $('#landscape-prompt').css('display', 'flex');
            $('.game-container').css('display', 'none');
        } else if (isMobile && !isPortrait) {
            // 移动端横屏：显示游戏内容
            $('#landscape-prompt').css('display', 'none');
            $('.game-container').css('display', 'flex');
        } else {
            // 桌面端：始终显示游戏内容
            $('#landscape-prompt').css('display', 'none');
            $('.game-container').css('display', 'flex');
        }

        requestAnimationFrame(() => {
            renderHands();
        });
    }, 250);

    $(window).on('resize', handleResize);

    $(window).trigger('resize');



    $('.help-panel').on('click', function(e) {
        if (e.target === this) {
            $(this).addClass('hidden');
        }
    });

    initDOMCache();

    $(window).on('beforeunload', function() {
        if (!gameState.gameOver && gameState.currentRound > 1) {
            saveGameState();
        }
    });

    $('#viewScoresBtn').on('click', function() {
        if (gameState.scoreHistory && gameState.scoreHistory.length > 0) {
            showDetailedScoreboard(null, false, true);
        }
    });

    newGame();

    // Initialize fullscreen button state
    updateFullscreenButton();
});

function toggleFullscreen() {
    if (!document.fullscreenElement &&
        !document.webkitFullscreenElement &&
        !document.mozFullScreenElement &&
        !document.msFullscreenElement) {
        // Enter fullscreen
        const element = document.documentElement;
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    } else {
        // Exit fullscreen
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
}

function updateFullscreenButton() {
    const isFullscreen = document.fullscreenElement ||
                       document.webkitFullscreenElement ||
                       document.mozFullScreenElement ||
                       document.msFullscreenElement;

    const button = $('#fullscreenBtn');
    if (isFullscreen) {
        button.text('⛶').attr('title', 'Exit Fullscreen (Ctrl+F)');
    } else {
        button.text('⛶').attr('title', 'Enter Fullscreen (Ctrl+F)');
    }
}

function autoFullscreen() {
    if (!document.fullscreenElement &&
        !document.webkitFullscreenElement &&
        !document.mozFullScreenElement &&
        !document.msFullscreenElement) {

        const element = document.documentElement;
        if (element.requestFullscreen) {
            element.requestFullscreen().catch(() => {});
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    }
}
