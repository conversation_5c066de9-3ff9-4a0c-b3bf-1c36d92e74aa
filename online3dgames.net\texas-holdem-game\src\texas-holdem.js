
"use strict";

var STARTING_BANKROLL = 1000;
var SMALL_BLIND = 10;
var BIG_BLIND = 20;
var cards = new Array(52);
var players;
var board = new Array(5);
var deck_index, button_index;
var current_bettor_index, current_bet_amount, current_min_raise;

function player(name, bankroll, carda, cardb, status, total_bet, subtotal_bet) {
  this.name = name;
  this.bankroll = bankroll;
  this.carda = carda;
  this.cardb = cardb;
  this.status = status;
  this.total_bet = total_bet;
  this.subtotal_bet = subtotal_bet;
}

function make_deck() {
  var i;
  var j = 0;
  for (i = 2; i < 15; i++) {
    cards[j++] = "h" + i;
    cards[j++] = "d" + i;
    cards[j++] = "c" + i;
    cards[j++] = "s" + i;
  }
}

function new_shuffle() {
  function get_random_int(max) {
    return Math.floor(Math.random() * max);
  }
  var len = cards.length;
  for (var i = 0; i < len; ++i) {
    var j = i + get_random_int(len - i);
    var tmp = cards[i];
    cards[i] = cards[j];
    cards[j] = tmp;
  }
}

function shuffle() {
  new_shuffle();
  deck_index = 0;
}

function new_game() {
  players = new Array(6);
  players[0] = new player("You", STARTING_BANKROLL, "", "", "", 0, 0);
  players[1] = new player("Player 1", STARTING_BANKROLL, "", "", "", 0, 0);
  players[2] = new player("Player 2", STARTING_BANKROLL, "", "", "", 0, 0);
  players[3] = new player("Player 3", STARTING_BANKROLL, "", "", "", 0, 0);
  players[4] = new player("Player 4", STARTING_BANKROLL, "", "", "", 0, 0);
  players[5] = new player("Player 5", STARTING_BANKROLL, "", "", "", 0, 0);

  button_index = 0;
  make_deck();
  new_round();
}

function new_round() {
  clear_table();

  clear_all_player_actions();

  for (var i = 0; i < players.length; i++) {
    players[i].carda = "";
    players[i].cardb = "";
    players[i].status = "";
    players[i].total_bet = 0;
    players[i].subtotal_bet = 0;
  }

  board = new Array(5);
  current_bet_amount = 0;
  current_min_raise = BIG_BLIND;


  update_dealer_button_position();

  shuffle();
  blinds_and_deal();
}

function clear_table() {

  var player_cards = ['#player-card-1', '#player-card-2'];
  for (var i = 0; i < player_cards.length; i++) {
    var element = document.querySelector(player_cards[i]);
    if (element) {
      element.innerHTML = '';
      element.className = 'card-slot';
    }
  }


  for (var i = 1; i < players.length; i++) {
    var ai_cards = ['#ai-player-' + i + '-card-1', '#ai-player-' + i + '-card-2'];
    for (var j = 0; j < ai_cards.length; j++) {
      var element = document.querySelector(ai_cards[j]);
      if (element) {
        element.innerHTML = '';
        element.className = 'card-slot';
      }
    }
  }

  var community_cards = ['#flop-1', '#flop-2', '#flop-3', '#turn', '#river'];
  for (var i = 0; i < community_cards.length; i++) {
    var element = document.querySelector(community_cards[i]);
    if (element) {
      element.innerHTML = '';
      element.className = 'card-slot';
    }
  }

  var bet_elements = document.querySelectorAll('.bet-amount');
  for (var i = 0; i < bet_elements.length; i++) {
    bet_elements[i].textContent = '0';
  }

  var player_seats = document.querySelectorAll('.player-seat');
  for (var i = 0; i < player_seats.length; i++) {
    player_seats[i].classList.remove('active', 'winner', 'folded');
  }
}

function update_dealer_button_position() {
  var all_players = document.querySelectorAll('.player-seat');
  for (var i = 0; i < all_players.length; i++) {
    all_players[i].classList.remove('dealer', 'small-blind', 'big-blind');
  }

  var suffix = get_table_suffix();

  var dealer_element = document.querySelector('#player-' + button_index + suffix);
  if (dealer_element) {
    dealer_element.classList.add('dealer');
  }

  var small_blind_pos = get_next_player_position(button_index, 1);
  var big_blind_pos = get_next_player_position(small_blind_pos, 1);

  var small_blind_element = document.querySelector('#player-' + small_blind_pos + suffix);
  if (small_blind_element) {
    small_blind_element.classList.add('small-blind');
  }

  var big_blind_element = document.querySelector('#player-' + big_blind_pos + suffix);
  if (big_blind_element) {
    big_blind_element.classList.add('big-blind');
  }
}

function blinds_and_deal() {
  var small_blind = get_next_player_position(button_index, 1);
  var big_blind = get_next_player_position(small_blind, 1);

  update_game_status('Dealer: ' + players[button_index].name +
                    ', Small Blind: ' + players[small_blind].name +
                    ', Big Blind: ' + players[big_blind].name);

  the_bet_function(small_blind, SMALL_BLIND);
  the_bet_function(big_blind, BIG_BLIND);
  players[big_blind].status = "OPTION";

  current_bettor_index = get_next_player_position(big_blind, 1);

  setTimeout(function() {
    deal_and_write_a();
  }, 500);
}

function get_next_player_position(current_pos, direction) {
  var next_pos = current_pos;
  do {
    next_pos = (next_pos + direction) % players.length;
    if (next_pos < 0) next_pos = players.length - 1;
  } while (players[next_pos].status == "BUST" && next_pos != current_pos);

  return next_pos;
}

function the_bet_function(player_index, bet_amount) {
  if (bet_amount > players[player_index].bankroll) {
    bet_amount = players[player_index].bankroll;
  }

  players[player_index].bankroll -= bet_amount;
  players[player_index].subtotal_bet += bet_amount;

  if (players[player_index].subtotal_bet > current_bet_amount) {
    current_bet_amount = players[player_index].subtotal_bet;
  }

  return true;
}

function deal_and_write_a() {
  update_game_status('Dealing cards...');
  var first_to_deal = get_next_player_position(button_index, 1);
  deal_cards_in_order('a', first_to_deal, 0);
}

function deal_cards_in_order(card_type, current_player_index, cards_dealt_count) {
  if (cards_dealt_count >= players.length) {
    if (card_type === 'a') {
      setTimeout(function() {
        var first_to_deal = get_next_player_position(button_index, 1);
        deal_cards_in_order('b', first_to_deal, 0);
      }, 125);
    } else {
      setTimeout(function() {
        update_ui();
        update_game_status('Pre-flop betting round begins');
        setTimeout(main, 250);
      }, 125);
    }
    return;
  }

  if (players[current_player_index].status != "BUST") {
    if (card_type === 'a') {
      players[current_player_index].carda = cards[deck_index++];
    } else {
      players[current_player_index].cardb = cards[deck_index++];
    }

    deal_card_with_flying_animation(current_player_index, card_type, function() {
      setTimeout(function() {
        var next_player = get_next_player_position(current_player_index, 1);
        deal_cards_in_order(card_type, next_player, cards_dealt_count + 1);
      }, 50);
    });
  } else {
    var next_player = get_next_player_position(current_player_index, 1);
    deal_cards_in_order(card_type, next_player, cards_dealt_count + 1);
  }
}

function deal_card_with_flying_animation(player_index, card_type, callback) {
  var flying_card = document.createElement('div');
  flying_card.className = 'flying-card';
  flying_card.innerHTML = '🂠';

  var game_table = document.querySelector('.poker-table[style*="flex"]');
  if (!game_table) {
    update_player_card_with_animation(player_index, card_type);
    if (callback) callback();
    return;
  }
  var table_rect = game_table.getBoundingClientRect();
  var start_x = table_rect.width / 2;
  var start_y = table_rect.height / 2;

  flying_card.style.position = 'absolute';
  flying_card.style.left = start_x + 'px';
  flying_card.style.top = start_y + 'px';
  flying_card.style.width = '50px';
  flying_card.style.height = '78px';
  flying_card.style.background = 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)';
  flying_card.style.borderRadius = '8px';
  flying_card.style.display = 'flex';
  flying_card.style.alignItems = 'center';
  flying_card.style.justifyContent = 'center';
  flying_card.style.fontSize = '2rem';
  flying_card.style.color = 'white';
  flying_card.style.zIndex = '1000';
  flying_card.style.transition = 'all 0.4s ease-out';
  flying_card.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';

  game_table.appendChild(flying_card);

  var suffix = get_table_suffix();
  var target_selector = '#player-' + player_index + '-card-' + (card_type === 'a' ? '1' : '2') + suffix;

  var target_element = document.querySelector(target_selector);
  if (target_element) {
    var target_rect = target_element.getBoundingClientRect();
    var target_x = target_rect.left - table_rect.left + target_rect.width / 2 - 30;
    var target_y = target_rect.top - table_rect.top + target_rect.height / 2 - 42;

    setTimeout(function() {
      flying_card.style.left = target_x + 'px';
      flying_card.style.top = target_y + 'px';
      flying_card.style.transform = 'rotate(360deg)';
    }, 50);

    setTimeout(function() {
      flying_card.remove();
      update_player_card_with_animation(player_index, card_type);
      if (callback) callback();
    }, 200);
  } else {
    flying_card.remove();
    if (callback) callback();
  }
}

function main() {
  if (get_num_betting() <= 1) {
    handle_end_of_round();
    return;
  }

  var increment_bettor_index = 0;

  if (players[current_bettor_index].status == "BUST" ||
      players[current_bettor_index].status == "FOLD") {
    increment_bettor_index = 1;
  } else if (!has_money(current_bettor_index)) {
    players[current_bettor_index].status = "CALL";
    increment_bettor_index = 1;
  } else if (players[current_bettor_index].status == "CALL" &&
             players[current_bettor_index].subtotal_bet == current_bet_amount) {
    increment_bettor_index = 1;
  } else {
    players[current_bettor_index].status = "";

    highlight_current_player();

    if (current_bettor_index == 0) {
      clear_all_player_actions();
      enable_player_actions(true);
      return;
    } else {
      setTimeout(function() { process_ai_turn(current_bettor_index); }, 1500);
      return;
    }
  }

  if (increment_bettor_index) {
    current_bettor_index = get_next_player_position(current_bettor_index, 1);

    if (is_betting_round_complete()) {
      ready_for_next_card();
    } else {
      setTimeout(main, 100);
    }
  }
}

function has_money(player_index) {
  return players[player_index].bankroll > 0;
}

function is_betting_round_complete() {
  var active_players = 0;
  var players_acted = 0;
  var player_details = [];

  for (var i = 0; i < players.length; i++) {
    if (players[i].status != "BUST" && players[i].status != "FOLD") {
      active_players++;

      var has_acted = false;
      if (players[i].status == "CALL" || players[i].status == "OPTION") {
        has_acted = true;
      } else if (!has_money(i) && players[i].subtotal_bet >= current_bet_amount) {
        has_acted = true;
      }

      if (has_acted) {
        players_acted++;
      }
      player_details.push(players[i].name + '(Status:' + players[i].status +
                         ', Bet:' + players[i].subtotal_bet +
                         ', Current Requirement:' + current_bet_amount +
                         ', Has Acted:' + has_acted + ')');
    }
  }

  var is_complete = players_acted == active_players && active_players > 0;
  return is_complete;
}

function ready_for_next_card() {
  // Clear all player action bubbles (new betting round begins)
  clear_all_player_actions();

  for (var i = 0; i < players.length; i++) {
    players[i].total_bet += players[i].subtotal_bet;
    players[i].subtotal_bet = 0;
  }

  current_bet_amount = 0;
  current_min_raise = BIG_BLIND;

  if (get_num_betting() <= 1) {
    handle_end_of_round();
    return;
  }

  current_bettor_index = get_next_player_position(button_index, 1);

  for (var i = 0; i < players.length; i++) {
    if (players[i].status == "CALL" || players[i].status == "OPTION") {
      players[i].status = "";
    }
  }

  if (!board[0]) {
    deal_flop();
  } else if (!board[3]) {
    deal_fourth();
  } else if (!board[4]) {
    deal_fifth();
  } else {
    handle_end_of_round();
  }
}

function get_num_betting() {
  var count = 0;
  var active_players = [];
  for (var i = 0; i < players.length; i++) {
    if (players[i].status != "BUST" && players[i].status != "FOLD") {
      count++;
      active_players.push(players[i].name + '(' + players[i].status + ')');
    }
  }
  return count;
}

function deal_flop() {
  deck_index++; 
  update_game_status('Dealing flop...');

  for (var i = 0; i < 3; i++) {
    (function(index, delay) {
      setTimeout(function() {
        board[index] = cards[deck_index++];
        update_community_card_with_animation(index);
        if (index === 2) {
          setTimeout(function() {
            update_game_status('Flop betting round begins');
            setTimeout(main, 500); 
          }, 150); 
        }
      }, delay);
    })(i, i * 200); 
  }
}

function deal_fourth() {
  deck_index++; 
  update_game_status('Dealing turn...');

  setTimeout(function() {
    board[3] = cards[deck_index++];
    update_community_card_with_animation(3);
    setTimeout(function() {
      update_game_status('Turn betting round begins');
      setTimeout(main, 500); 
    }, 250); 
  }, 200); 
}

function deal_fifth() {
  deck_index++; 
  update_game_status('Dealing river...');

  setTimeout(function() {
    board[4] = cards[deck_index++];
    update_community_card_with_animation(4);
    setTimeout(function() {
      update_game_status('River betting round begins');
      setTimeout(main, 500); 
    }, 250); 
  }, 200); 
}

function handle_end_of_round() {
  show_all_player_cards();

  setTimeout(function() {
    var winners = determine_winners();

    var total_pot = 0;
    for (var i = 0; i < players.length; i++) {
      total_pot += players[i].total_bet;
    }
    var win_amount = distribute_pot(winners);
    show_results(winners, win_amount);

    button_index = get_next_player_position(button_index, 1);
  }, 2000);
}

function evaluate_hand(player_cards, community_cards, debug_player_name) {
  var all_cards = player_cards.concat(community_cards);
  var ranks = [];
  var suits = [];

  for (var i = 0; i < all_cards.length; i++) {
    if (all_cards[i]) {
      var suit = all_cards[i].charAt(0);
      var rank = parseInt(all_cards[i].substring(1));
      ranks.push(rank);
      suits.push(suit);
    }
  }

  ranks.sort(function(a, b) { return b - a; });

  var rank_counts = {};
  for (var i = 0; i < ranks.length; i++) {
    rank_counts[ranks[i]] = (rank_counts[ranks[i]] || 0) + 1;
  }

  var suit_counts = {};
  for (var i = 0; i < suits.length; i++) {
    suit_counts[suits[i]] = (suit_counts[suits[i]] || 0) + 1;
  }


  var is_flush = false;
  for (var suit in suit_counts) {
    if (suit_counts[suit] >= 5) {
      is_flush = true;
      break;
    }
  }

  var is_straight = false;
  var straight_high = 0;
  var unique_ranks = Object.keys(rank_counts).map(function(r) { return parseInt(r); }).sort(function(a, b) { return b - a; });

  if (unique_ranks.includes(14) && unique_ranks.includes(2) && unique_ranks.includes(3) && unique_ranks.includes(4) && unique_ranks.includes(5)) {
    is_straight = true;
    straight_high = 5;
  } else {
    for (var i = 0; i <= unique_ranks.length - 5; i++) {
      var consecutive = true;
      for (var j = 1; j < 5; j++) {
        if (unique_ranks[i + j] !== unique_ranks[i] - j) {
          consecutive = false;
          break;
        }
      }
      if (consecutive) {
        is_straight = true;
        straight_high = unique_ranks[i];
        break;
      }
    }
  }

  var pairs = [];
  var three_kind = 0;
  var four_kind = 0;

  for (var rank in rank_counts) {
    if (rank_counts[rank] === 4) {
      four_kind = parseInt(rank);
    } else if (rank_counts[rank] === 3) {
      three_kind = parseInt(rank);
    } else if (rank_counts[rank] === 2) {
      pairs.push(parseInt(rank));
    }
  }

  pairs.sort(function(a, b) { return b - a; });

  var hand_rank = 0;
  var hand_name = '';
  var tie_breakers = [];

  function get_kickers(excluded_ranks, count) {
    var kickers = [];
    for (var i = 0; i < ranks.length && kickers.length < count; i++) {
      if (excluded_ranks.indexOf(ranks[i]) === -1) {
        kickers.push(ranks[i]);
      }
    }
    return kickers;
  }

  if (is_straight && is_flush) {
    hand_rank = 8; 
    hand_name = 'Straight Flush';
    tie_breakers = [straight_high];
  } else if (four_kind > 0) {
    hand_rank = 7; 
    hand_name = 'Four of a Kind';
    var kickers = get_kickers([four_kind], 1);
    tie_breakers = [four_kind].concat(kickers);
  } else if (three_kind > 0 && pairs.length > 0) {
    hand_rank = 6; 
    hand_name = 'Full House';
    tie_breakers = [three_kind, pairs[0]];
  } else if (is_flush) {
    hand_rank = 5; 
    hand_name = 'Flush';
    tie_breakers = ranks.slice(0, 5);
  } else if (is_straight) {
    hand_rank = 4; // Straight
    hand_name = 'Straight';
    tie_breakers = [straight_high];
  } else if (three_kind > 0) {
    hand_rank = 3; // Three of a Kind
    hand_name = 'Three of a Kind';
    var kickers = get_kickers([three_kind], 2);
    tie_breakers = [three_kind].concat(kickers);
  } else if (pairs.length >= 2) {
    hand_rank = 2; // Two Pair
    hand_name = 'Two Pair';
    var kickers = get_kickers([pairs[0], pairs[1]], 1);
    tie_breakers = [pairs[0], pairs[1]].concat(kickers);
  } else if (pairs.length === 1) {
    hand_rank = 1; // One Pair
    hand_name = 'One Pair';
    var kickers = get_kickers([pairs[0]], 3);
    tie_breakers = [pairs[0]].concat(kickers);
  } else {
    hand_rank = 0; // High Card
    hand_name = 'High Card';
    tie_breakers = ranks.slice(0, 5);
  }



  return {
    rank: hand_rank,
    name: hand_name,
    tie_breakers: tie_breakers,
    debug_info: {
      all_cards: all_cards,
      rank_counts: rank_counts,
      pairs: pairs,
      three_kind: three_kind,
      four_kind: four_kind
    }
  };
}

function determine_winners() {
  var active_players = [];
  for (var i = 0; i < players.length; i++) {
    if (players[i].status != "BUST" && players[i].status != "FOLD") {
      active_players.push(i);
    }
  }

  if (active_players.length == 1) {
    return {
      winners: [active_players[0]],
      reason: players[active_players[0]].name + ' wins (others folded)',
      hand_type: 'Others folded'
    };
  }

  var player_hands = [];
  for (var i = 0; i < active_players.length; i++) {
    var player_index = active_players[i];
    var player_cards = [players[player_index].carda, players[player_index].cardb];
    var hand_evaluation = evaluate_hand(player_cards, board, players[player_index].name);
    player_hands.push({
      player_index: player_index,
      evaluation: hand_evaluation
    });
  }

  var best_rank = -1;
  for (var i = 0; i < player_hands.length; i++) {
    if (player_hands[i].evaluation.rank > best_rank) {
      best_rank = player_hands[i].evaluation.rank;
    }
  }

  var candidates = [];
  for (var i = 0; i < player_hands.length; i++) {
    if (player_hands[i].evaluation.rank === best_rank) {
      candidates.push(player_hands[i]);
    }
  }

  if (candidates.length === 1) {
    return {
      winners: [candidates[0].player_index],
      reason: players[candidates[0].player_index].name + ' wins (' + candidates[0].evaluation.name + ')',
      hand_type: candidates[0].evaluation.name
    };
  }

  var winners = [candidates[0]];
  for (var i = 1; i < candidates.length; i++) {
    var comparison = compare_tie_breakers(candidates[i].evaluation.tie_breakers, winners[0].evaluation.tie_breakers);
    if (comparison > 0) {
      winners = [candidates[i]];
    } else if (comparison === 0) {
      winners.push(candidates[i]);
    }
  }

  var winner_names = winners.map(function(w) { return players[w.player_index].name; }).join(', ');
  var winner_indices = winners.map(function(w) { return w.player_index; });

  return {
    winners: winner_indices,
    reason: winner_names + ' wins (' + winners[0].evaluation.name + ')',
    hand_type: winners[0].evaluation.name
  };
}

function compare_tie_breakers(a, b) {
  for (var i = 0; i < Math.min(a.length, b.length); i++) {
    if (a[i] > b[i]) return 1;
    if (a[i] < b[i]) return -1;
  }
  return 0;
}

function distribute_pot(result) {
  var total_pot = 0;
  for (var i = 0; i < players.length; i++) {
    total_pot += players[i].total_bet + players[i].subtotal_bet;
  }
  var win_amount = Math.floor(total_pot / result.winners.length);

  for (var j = 0; j < result.winners.length; j++) {
    players[result.winners[j]].bankroll += win_amount;
  }

  for (var i = 0; i < players.length; i++) {
    players[i].total_bet = 0;
    players[i].subtotal_bet = 0;
  }

  update_ui();

  update_game_status(result.reason + ' - Won $' + win_amount);

  return win_amount;
}

function show_results(result, win_amount) {
  update_game_status(result.reason);
  var suffix = get_table_suffix();

  for (var i = 0; i < result.winners.length; i++) {
    var winner_element = document.querySelector('#player-' + result.winners[i] + suffix);
    if (winner_element) {
      winner_element.classList.add('winner');
    }
  }

  setTimeout(function() {
    show_game_result_modal(result, win_amount);
  }, 1500); 
}

function show_game_result_modal(result, win_amount) {
  var winner_names = result.winners.map(function(index) {
    return players[index].name;
  }).join(' and ');

  create_custom_result_modal(result, winner_names, win_amount);
}

function create_custom_result_modal(result, winner_names, win_amount) {
  var existing_modal = document.getElementById('custom-result-modal');
  if (existing_modal) {
    existing_modal.remove();
  }

  var modal_overlay = document.createElement('div');
  modal_overlay.id = 'custom-result-modal';
  modal_overlay.className = 'custom-modal-overlay';

  var modal_content = document.createElement('div');
  modal_content.className = 'custom-modal-content result-modal-content';

  var header = document.createElement('div');
  header.className = 'result-header';

  var icon = document.createElement('i');
  icon.className = 'fas fa-trophy result-icon';
  header.appendChild(icon);

  var title = document.createElement('h2');
  title.className = 'result-title';
  title.textContent = 'Game Over';
  header.appendChild(title);

  modal_content.appendChild(header);

  var body = document.createElement('div');
  body.className = 'result-body';

  var winner_info = document.createElement('div');
  winner_info.className = 'winner-info';

  var winner_name = document.createElement('div');
  winner_name.className = 'winner-name';
  winner_name.textContent = winner_names + ' Wins!';
  winner_info.appendChild(winner_name);

  var hand_type = document.createElement('div');
  hand_type.className = 'hand-type';
  hand_type.textContent = 'Hand: ' + result.hand_type;
  winner_info.appendChild(hand_type);

  var win_amount_div = document.createElement('div');
  win_amount_div.className = 'win-amount';
  win_amount_div.textContent = 'Prize: $' + win_amount;
  winner_info.appendChild(win_amount_div);

  body.appendChild(winner_info);

  var comparison_section = document.createElement('div');
  comparison_section.className = 'hand-comparison-section';

  var comparison_title = document.createElement('h3');
  comparison_title.textContent = 'Hand Comparison';
  comparison_section.appendChild(comparison_title);

  var community_section = document.createElement('div');
  community_section.className = 'community-cards-display';

  var community_title = document.createElement('div');
  community_title.className = 'community-title';
  community_title.textContent = 'Community Cards';
  community_section.appendChild(community_title);

  var community_cards = document.createElement('div');
  community_cards.className = 'community-cards-row';

  for (var j = 0; j < board.length; j++) {
    if (board[j]) {
      var community_card = document.createElement('div');
      community_card.className = 'card-small community-card';
      community_card.textContent = format_card_display(board[j]);
      set_card_color(community_card, board[j]); 
      community_cards.appendChild(community_card);
    }
  }

  community_section.appendChild(community_cards);
  comparison_section.appendChild(community_section);

  var hands_comparison = document.createElement('div');
  hands_comparison.className = 'hands-comparison';

  for (var i = 0; i < players.length; i++) {
    if (players[i].status !== "BUST" && players[i].status !== "FOLD") {
      var is_winner = result.winners.includes(i);
      var player_comparison = document.createElement('div');
      player_comparison.className = 'player-comparison' + (is_winner ? ' winner-comparison' : '');

      var player_header = document.createElement('div');
      player_header.className = 'player-comparison-header';

      var player_name = document.createElement('div');
      player_name.className = 'player-name-comparison';
      player_name.textContent = players[i].name;
      if (is_winner) {
        var crown = document.createElement('i');
        crown.className = 'fas fa-crown winner-crown';
        player_name.appendChild(crown);
      }
      player_header.appendChild(player_name);

      var player_cards = document.createElement('div');
      player_cards.className = 'player-cards-comparison';

      if (players[i].carda) {
        var card1 = document.createElement('div');
        card1.className = 'card-small player-card';
        card1.textContent = format_card_display(players[i].carda);
        set_card_color(card1, players[i].carda);
        player_cards.appendChild(card1);
      }

      if (players[i].cardb) {
        var card2 = document.createElement('div');
        card2.className = 'card-small player-card';
        card2.textContent = format_card_display(players[i].cardb);
        set_card_color(card2, players[i].cardb);
        player_cards.appendChild(card2);
      }

      var player_cards_array = [players[i].carda, players[i].cardb];
      var hand_eval = evaluate_hand(player_cards_array, board);

      var hand_result = document.createElement('div');
      hand_result.className = 'hand-result';

      var hand_type = document.createElement('div');
      hand_type.className = 'hand-type-name';
      hand_type.textContent = hand_eval.name;
      hand_result.appendChild(hand_type);

      var hand_strength = document.createElement('div');
      hand_strength.className = 'hand-strength';
      hand_strength.textContent = 'Strength: ' + hand_eval.rank + '/8';
      hand_result.appendChild(hand_strength);

      player_cards.appendChild(hand_result);

      player_comparison.appendChild(player_header);
      player_comparison.appendChild(player_cards);
      hands_comparison.appendChild(player_comparison);
    }
  }

  comparison_section.appendChild(hands_comparison);

  body.appendChild(comparison_section);
  modal_content.appendChild(body);

  var footer = document.createElement('div');
  footer.className = 'result-footer';

  var continue_btn = document.createElement('button');
  continue_btn.className = 'continue-game-btn';
  continue_btn.textContent = 'Continue Game';
  continue_btn.onclick = continue_next_round;

  var home_btn = document.createElement('button');
  home_btn.className = 'home-btn';
  home_btn.textContent = 'Home';
  home_btn.onclick = function() {
    window.location.href = '/';
  };

  footer.appendChild(continue_btn);
  footer.appendChild(home_btn);
  modal_content.appendChild(footer);

  modal_overlay.appendChild(modal_content);
  document.body.appendChild(modal_overlay);

  setTimeout(function() {
    modal_overlay.classList.add('show');
  }, 10);
}

function format_card_display(card) {
  if (!card) return '';

  var rank = get_card_rank(card);
  var suit = get_suit_symbol(card);

  return rank + suit;
}

function set_card_color(element, card) {
  if (!element || !card) return;

  var color = get_card_color(card);

  element.classList.remove('red', 'black', 'red-suit', 'black-suit');

  if (color === 'red') {
    element.classList.add('red-suit');
  } else {
    element.classList.add('black-suit');
  }
}

function show_player_action(player_id, action, amount) {
  var suffix = get_table_suffix();
  var player_element = document.getElementById('player-' + player_id + suffix);
  if (!player_element) return;

  var action_element = player_element.querySelector('.player-action');
  if (!action_element) return;

  var action_text = '';
  var action_color = '#333';

  switch(action) {
    case 'fold':
      action_text = 'Fold';
      action_color = '#e74c3c';
      break;
    case 'call':
      action_text = amount ? 'Call $' + amount : 'Call';
      action_color = '#3498db';
      break;
    case 'raise':
      action_text = 'Raise $' + amount;
      action_color = '#e67e22';
      break;
    case 'check':
      action_text = 'Check';
      action_color = '#27ae60';
      break;
    case 'bet':
      action_text = 'Bet $' + amount;
      action_color = '#f39c12';
      break;
    case 'all-in':
      action_text = 'All-in';
      action_color = '#9b59b6';
      break;
    default:
      action_text = action;
  }

  action_element.textContent = action_text;
  action_element.style.color = action_color;
  action_element.classList.add('show');
}

function clear_all_player_actions() {
  var max_players = current_player_count || 6;
  var suffix = get_table_suffix();

  for (var i = 0; i < max_players; i++) {
    var player_element = document.getElementById('player-' + i + suffix);
    if (player_element) {
      var action_element = player_element.querySelector('.player-action');
      if (action_element) {
        action_element.classList.remove('show');
        action_element.textContent = '';
      }
    }
  }
}

function clear_all_player_cards() {
  var max_players = current_player_count || 6;
  var suffix = get_table_suffix();

  for (var i = 0; i < max_players; i++) {
    var player_element = document.getElementById('player-' + i + suffix);
    if (player_element) {
      var card1_element = player_element.querySelector('#player-' + i + '-card-1' + suffix);
      if (card1_element) {
        card1_element.className = 'card-slot';
        card1_element.textContent = '';
        card1_element.style.color = '';
      }

      var card2_element = player_element.querySelector('#player-' + i + '-card-2' + suffix);
      if (card2_element) {
        card2_element.className = 'card-slot';
        card2_element.textContent = '';
        card2_element.style.color = '';
      }
    }
  }

function clear_community_cards_display() {
  var suffix = get_table_suffix();
  var community_card_ids = [
    '#flop-1' + suffix,
    '#flop-2' + suffix,
    '#flop-3' + suffix,
    '#turn' + suffix,
    '#river' + suffix
  ];

  for (var i = 0; i < community_card_ids.length; i++) {
    var card_element = document.querySelector(community_card_ids[i]);
    if (card_element) {
      card_element.className = 'card-slot';
      card_element.innerHTML = '';
    }
  }
}

  for (var j = 0; j < 5; j++) {
    var community_card = document.getElementById('community-card-' + (j + 1) + suffix);
    if (community_card) {
      community_card.className = 'card-slot';
      community_card.textContent = '';
      community_card.style.color = '';
    }
  }
}

function get_table_suffix() {
  if (current_player_count === 3) {
    return '-3p';
  } else if (current_player_count === 9) {
    return '-9p';
  } else {
    return '';
  }
}


function continue_next_round() {
  clear_all_player_actions();

  var all_players = document.querySelectorAll('.player-seat');
  for (var i = 0; i < all_players.length; i++) {
    all_players[i].classList.remove('winner');
  }

  var custom_modal = document.getElementById('custom-result-modal');
  if (custom_modal) {
    custom_modal.classList.remove('show');
    setTimeout(function() {
      custom_modal.remove();
    }, 300);
  }

  if (typeof gameModal !== 'undefined') {
    gameModal.hide();
  }

  clear_all_player_cards();

  new_round();
}

function update_ui() {
  for (var i = 0; i < players.length; i++) {
    update_player_ui(i);
  }

  var total_pot = 0;
  for (var i = 0; i < players.length; i++) {
    total_pot += players[i].total_bet + players[i].subtotal_bet;
  }

  var suffix = get_table_suffix();
  var pot_amount_id = 'pot-amount' + (suffix === '' ? '' : suffix);
  var pot_amount = document.getElementById(pot_amount_id);
  if (pot_amount) pot_amount.textContent = total_pot;

  var current_bet = document.getElementById('current-bet');
  var current_bet_display = document.getElementById('current-bet-display');
  if (current_bet) current_bet.textContent = current_bet_amount;
  if (current_bet_display) current_bet_display.textContent = '$' + current_bet_amount;

  update_community_cards();
  highlight_current_player();
  update_player_chips_display();
}

function update_player_ui(player_index) {
  var player = players[player_index];
  var suffix = get_table_suffix();
  var player_element = document.querySelector('#player-' + player_index + suffix);

  if (!player_element) return;

  var chips_element = player_element.querySelector('.chips-amount');
  if (chips_element) {
    chips_element.textContent = player.bankroll;
  }

  var bet_element = player_element.querySelector('.bet-amount');
  if (bet_element) {
    bet_element.textContent = player.subtotal_bet;
  }

  player_element.classList.toggle('folded', player.status == 'FOLD');
}

function update_community_cards() {
}

function render_card_to_element(selector, card, with_animation) {
  var element = document.querySelector(selector);
  if (!element || !card) return;

  element.innerHTML = '';
  element.className = 'card ' + get_card_color(card);

  var rank_element = document.createElement('div');
  rank_element.className = 'card-rank';
  rank_element.textContent = get_card_rank(card);

  var suit_element = document.createElement('div');
  suit_element.className = 'card-suit';
  suit_element.textContent = get_suit_symbol(card);

  element.appendChild(rank_element);
  element.appendChild(suit_element);

  if (with_animation) {
    element.style.transform = 'scale(0) rotate(180deg)';
    element.style.opacity = '0';

    setTimeout(function() {
      element.style.transition = 'all 0.5s ease';
      element.style.transform = 'scale(1) rotate(0deg)';
      element.style.opacity = '1';
    }, 50);
  }
}

function get_card_rank(card) {
  var rank = card.substring(1);
  if (rank == '11') return 'J';
  if (rank == '12') return 'Q';
  if (rank == '13') return 'K';
  if (rank == '14') return 'A';
  return rank;
}

function get_card_color(card) {
  var suit = card.charAt(0);
  return (suit == 'h' || suit == 'd') ? 'red' : 'black';
}

function get_suit_symbol(card) {
  var suit = card.charAt(0);
  var symbols = {
    'h': '♥',
    'd': '♦',
    'c': '♣',
    's': '♠'
  };
  return symbols[suit] || suit;
}

function highlight_current_player() {
  document.querySelectorAll('.player-seat').forEach(function(seat) {
    seat.classList.remove('active');
  });

  var suffix = get_table_suffix();
  var current_player_element = document.querySelector('#player-' + current_bettor_index + suffix);
  if (current_player_element) {
    current_player_element.classList.add('active');
  }
}

function update_game_status(message) {
  var status_element = document.querySelector('#game-status .status-message');
  if (status_element) {
    status_element.textContent = message;
  }
}

function update_player_card_with_animation(player_index, card_type) {
  var suffix = get_table_suffix();

  if (player_index == 0) {
    var card = card_type == 'a' ? players[player_index].carda : players[player_index].cardb;
    var selector = '#player-0-card-' + (card_type == 'a' ? '1' : '2') + suffix;
    render_card_to_element(selector, card, false);
  } else {
    var selector = '#player-' + player_index + '-card-' + (card_type == 'a' ? '1' : '2') + suffix;
    render_card_back_to_element(selector);
  }
}

function render_card_back_to_element(selector) {
  var element = document.querySelector(selector);
  if (element) {
    element.innerHTML = '';
    element.className = 'card-back';
    element.style.transform = 'scale(1)';
    element.style.opacity = '1';

    var back_content = document.createElement('div');
    back_content.innerHTML = '🂠';
    back_content.style.fontSize = '2rem';
    back_content.style.color = 'white';
    element.appendChild(back_content);
  }
}

function update_community_card_with_animation(card_index) {
  var suffix = get_table_suffix();
  var card_slots = [
    '#community-card-1' + suffix,
    '#community-card-2' + suffix,
    '#community-card-3' + suffix,
    '#community-card-4' + suffix,
    '#community-card-5' + suffix
  ];

  if (card_slots[card_index] && board[card_index]) {
    deal_community_card_with_flying_animation(card_index, function() {
      render_card_to_element(card_slots[card_index], board[card_index], false);
    });
  }
}

function deal_community_card_with_flying_animation(card_index, callback) {
  var flying_card = document.createElement('div');
  flying_card.className = 'flying-card';
  flying_card.innerHTML = '🂠';

  var game_table = document.querySelector('.poker-table[style*="flex"]');
  if (!game_table) {
    if (callback) callback();
    return;
  }
  var table_rect = game_table.getBoundingClientRect();
  var start_x = table_rect.width / 2;
  var start_y = table_rect.height / 2;

  flying_card.style.position = 'absolute';
  flying_card.style.left = start_x + 'px';
  flying_card.style.top = start_y + 'px';
  flying_card.style.width = '60px';
  flying_card.style.height = '84px';
  flying_card.style.background = 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)';
  flying_card.style.borderRadius = '8px';
  flying_card.style.display = 'flex';
  flying_card.style.alignItems = 'center';
  flying_card.style.justifyContent = 'center';
  flying_card.style.fontSize = '2rem';
  flying_card.style.color = 'white';
  flying_card.style.zIndex = '1000';
  flying_card.style.transition = 'all 0.6s ease-out';
  flying_card.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';

  game_table.appendChild(flying_card);

  var suffix = get_table_suffix();
  var card_slots = [
    '#community-card-1' + suffix,
    '#community-card-2' + suffix,
    '#community-card-3' + suffix,
    '#community-card-4' + suffix,
    '#community-card-5' + suffix
  ];
  var target_element = document.querySelector(card_slots[card_index]);

  if (target_element) {
    var target_rect = target_element.getBoundingClientRect();
    var target_x = target_rect.left - table_rect.left + target_rect.width / 2 - 30;
    var target_y = target_rect.top - table_rect.top + target_rect.height / 2 - 42;

    setTimeout(function() {
      flying_card.style.left = target_x + 'px';
      flying_card.style.top = target_y + 'px';
      flying_card.style.transform = 'rotate(180deg)';
    }, 50);

    setTimeout(function() {
      flying_card.remove();
      if (callback) callback();
    }, 325);
  } else {
    flying_card.remove();
    if (callback) callback();
  }
}

function show_all_player_cards() {
  update_game_status('Showdown!');
  var suffix = get_table_suffix();

  for (var i = 0; i < players.length; i++) {
    if (players[i].status != "BUST" && players[i].status != "FOLD") {
      if (i > 0) {
        setTimeout(function(player_index) {
          return function() {
            var card1_selector = '#player-' + player_index + '-card-1' + suffix;
            var card2_selector = '#player-' + player_index + '-card-2' + suffix;

            if (players[player_index].carda) {
              render_card_to_element(card1_selector, players[player_index].carda, true);
            }
            if (players[player_index].cardb) {
              render_card_to_element(card2_selector, players[player_index].cardb, true);
            }
          };
        }(i), i * 500);
      }
    }
  }
}

function enable_player_actions(enabled) {
  var current_player = players[current_bettor_index];
  var call_amount = Math.max(0, current_bet_amount - current_player.subtotal_bet);

  var control_panel = document.getElementById('control-panel');
  if (control_panel) {
    if (enabled) {
      control_panel.style.display = 'flex';
      control_panel.classList.remove('hidden');
    } else {
      control_panel.classList.add('hidden');
      setTimeout(function() {
        control_panel.style.display = 'none';
      }, 300);
    }
  }

  document.getElementById('fold-btn').disabled = !enabled;
  document.getElementById('call-btn').disabled = !enabled;
  document.getElementById('raise-btn').disabled = !enabled;

  var call_text = document.getElementById('call-text');
  if (call_amount == 0) {
    call_text.textContent = 'Check';
  } else {
    call_text.textContent = 'Call ' + call_amount;
  }

  var bet_slider = document.getElementById('bet-slider');
  bet_slider.min = BIG_BLIND;
  bet_slider.max = current_player.bankroll;
  bet_slider.disabled = !enabled;

  update_ui();
}

function player_fold() {
  players[current_bettor_index].status = "FOLD";
  show_player_action(current_bettor_index, 'fold');
  enable_player_actions(false);
  update_ui();

  var remaining_players = get_num_betting();
  if (remaining_players <= 1) {
    handle_end_of_round();
    return;
  }

  current_bettor_index = get_next_player_position(current_bettor_index, 1);
  setTimeout(main, 500);
}

function player_call() {
  var current_player = players[current_bettor_index];
  var call_amount = Math.max(0, current_bet_amount - current_player.subtotal_bet);

  if (call_amount > 0) {
    the_bet_function(current_bettor_index, call_amount);
    show_player_action(current_bettor_index, 'call', call_amount);
  } else {
    show_player_action(current_bettor_index, 'check');
  }

  current_player.status = "CALL";
  enable_player_actions(false);
  update_ui();

  current_bettor_index = get_next_player_position(current_bettor_index, 1);
  setTimeout(main, 500);
}

function player_raise() {
  var current_player = players[current_bettor_index];
  var bet_slider = document.getElementById('bet-slider');
  var raise_amount = parseInt(bet_slider.value);

  var call_amount = Math.max(0, current_bet_amount - current_player.subtotal_bet);
  var total_bet = call_amount + raise_amount;

  the_bet_function(current_bettor_index, total_bet);
  current_player.status = "CALL";

  if (call_amount > 0) {
    show_player_action(current_bettor_index, 'raise', raise_amount);
  } else {
    show_player_action(current_bettor_index, 'bet', raise_amount);
  }

  enable_player_actions(false);
  update_ui();

  current_bettor_index = get_next_player_position(current_bettor_index, 1);
  setTimeout(main, 500);
}

function evaluate_ai_hand_strength(player_index) {
  var player_cards = [players[player_index].carda, players[player_index].cardb];

  var current_board = [];
  var game_stage = '';

  if (board.length === 0) {
    game_stage = 'preflop';
    current_board = [];
  } else if (board.length === 3) {
    game_stage = 'flop';
    current_board = board.slice(0, 3);
  } else if (board.length === 4) {
    game_stage = 'turn';
    current_board = board.slice(0, 4);
  } else if (board.length >= 5) {
    game_stage = 'river';
    current_board = board.slice(0, 5);
  }



  if (game_stage === 'preflop') {
    return evaluate_preflop_strength(player_cards);
  } else {
    return evaluate_postflop_strength(player_cards, current_board);
  }
}

function evaluate_preflop_strength(hole_cards) {
  var card1 = hole_cards[0];
  var card2 = hole_cards[1];

  if (!card1 || !card2) return 5;

  var rank1 = parseInt(card1.substring(1));
  var rank2 = parseInt(card2.substring(1));
  var suit1 = card1.charAt(0);
  var suit2 = card2.charAt(0);

  var high_rank = Math.max(rank1, rank2);
  var low_rank = Math.min(rank1, rank2);
  var is_pair = rank1 === rank2;
  var is_suited = suit1 === suit2;

  var strength = 0;

  if (is_pair) {
    if (high_rank >= 14) { // AA
      strength = 85;
    } else if (high_rank >= 13) { // KK
      strength = 80;
    } else if (high_rank >= 12) { // QQ
      strength = 75;
    } else if (high_rank >= 11) { // JJ
      strength = 70;
    } else if (high_rank >= 10) { // TT
      strength = 65;
    } else if (high_rank >= 9) { // 99
      strength = 60;
    } else if (high_rank >= 8) { // 88
      strength = 55;
    } else if (high_rank >= 7) { // 77
      strength = 50;
    } else if (high_rank >= 6) { // 66
      strength = 45;
    } else { // 22-55
      strength = 40;
    }
  } else {
    if (high_rank >= 14 && low_rank >= 13) { // AK
      strength = is_suited ? 75 : 70;
    } else if (high_rank >= 14 && low_rank >= 12) { // AQ
      strength = is_suited ? 65 : 60;
    } else if (high_rank >= 14 && low_rank >= 11) { // AJ
      strength = is_suited ? 55 : 50;
    } else if (high_rank >= 14 && low_rank >= 10) { // AT
      strength = is_suited ? 50 : 45;
    } else if (high_rank >= 14) { 
      strength = is_suited ? 40 : 30;
    } else if (high_rank >= 13 && low_rank >= 12) { // KQ
      strength = is_suited ? 60 : 55;
    } else if (high_rank >= 13 && low_rank >= 11) { // KJ
      strength = is_suited ? 50 : 45;
    } else if (high_rank >= 13 && low_rank >= 10) { // KT
      strength = is_suited ? 45 : 40;
    } else if (high_rank >= 12 && low_rank >= 11) { // QJ
      strength = is_suited ? 45 : 40;
    } else if (high_rank >= 12 && low_rank >= 10) { // QT
      strength = is_suited ? 40 : 35;
    } else if (high_rank >= 11 && low_rank >= 10) { // JT
      strength = is_suited ? 40 : 35;
    } else if (is_suited && high_rank >= 10) { 
      strength = 35;
    } else if (high_rank >= 11) { 
      strength = is_suited ? 25 : 20;
    } else if (high_rank >= 10) { 
      strength = is_suited ? 20 : 15;
    } else { 
      strength = is_suited ? 15 : 10;
    }
  }

  return strength;
}

function evaluate_postflop_strength(hole_cards, community_cards) {
  var hand_eval = evaluate_hand(hole_cards, community_cards);

  var strength = 0;

  if (hand_eval.rank >= 7) { 
    strength = 95;
  } else if (hand_eval.rank === 6) { 
    strength = 90;
  } else if (hand_eval.rank === 5) { 
    strength = 80;
  } else if (hand_eval.rank === 4) { 
    strength = 75;
  } else if (hand_eval.rank === 3) { 
    var three_rank = hand_eval.tie_breakers[0];
    if (three_rank >= 11) {
      strength = 70;
    } else if (three_rank >= 8) {
      strength = 65;
    } else {
      strength = 60;
    }
  } else if (hand_eval.rank === 2) { 
    var high_pair = hand_eval.tie_breakers[0];
    if (high_pair >= 11) {
      strength = 55;
    } else if (high_pair >= 8) {
      strength = 45;
    } else {
      strength = 35;
    }
  } else if (hand_eval.rank === 1) { 
    var pair_rank = hand_eval.tie_breakers[0];
    if (pair_rank >= 14) { 
      strength = 50;
    } else if (pair_rank >= 11) { 
      strength = 40;
    } else if (pair_rank >= 8) { 
      strength = 30;
    } else {
      strength = 20;
    }
  } else { 
    var high_card = hand_eval.tie_breakers[0];
    if (high_card >= 14) {
      strength = 15;
    } else if (high_card >= 11) {
      strength = 10;
    } else {
      strength = 5;
    }
  }

  return strength;
}

function process_ai_turn(player_index) {
  var current_player = players[player_index];

  var action = advanced_ai_decision(player_index);

  execute_ai_decision(player_index, action);
}

function get_ai_personality(player_index) {
  var personality_templates = [
    {
      name: 'aggressive',
      aggression: 0.75,
      bluff_frequency: 0.12,
      value_bet_size: 0.65,
      bluff_bet_size: 0.55,
      fold_threshold: 0.25
    },
    {
      name: 'tight_aggressive',
      aggression: 0.6,
      bluff_frequency: 0.08,
      value_bet_size: 0.6,
      bluff_bet_size: 0.5,
      fold_threshold: 0.3
    },
    {
      name: 'loose_passive',
      aggression: 0.4,
      bluff_frequency: 0.05,
      value_bet_size: 0.5,
      bluff_bet_size: 0.4,
      fold_threshold: 0.2
    },
    {
      name: 'balanced',
      aggression: 0.65,
      bluff_frequency: 0.1,
      value_bet_size: 0.6,
      bluff_bet_size: 0.5,
      fold_threshold: 0.25
    },
    {
      name: 'loose_aggressive',
      aggression: 0.8,
      bluff_frequency: 0.15,
      value_bet_size: 0.7,
      bluff_bet_size: 0.6,
      fold_threshold: 0.2
    },
    {
      name: 'calling_station',
      aggression: 0.3,
      bluff_frequency: 0.03,
      value_bet_size: 0.45,
      bluff_bet_size: 0.35,
      fold_threshold: 0.15
    },
    {
      name: 'maniac',
      aggression: 0.9,
      bluff_frequency: 0.2,
      value_bet_size: 0.8,
      bluff_bet_size: 0.7,
      fold_threshold: 0.2
    },
    {
      name: 'rock',
      aggression: 0.5,
      bluff_frequency: 0.04,
      value_bet_size: 0.55,
      bluff_bet_size: 0.4,
      fold_threshold: 0.25
    }
  ];

  if (player_index === 0) {
    return null;
  }

  var template_index = (player_index - 1) % personality_templates.length;
  var base_personality = personality_templates[template_index];

  var variation = 0.1; 
  var random_factor = (Math.random() - 0.5) * variation;

  return {
    name: base_personality.name + '_' + player_index,
    aggression: Math.max(0.1, Math.min(0.95, base_personality.aggression + random_factor)),
    bluff_frequency: Math.max(0.01, Math.min(0.3, base_personality.bluff_frequency + random_factor * 0.5)),
    value_bet_size: Math.max(0.3, Math.min(0.9, base_personality.value_bet_size + random_factor)),
    bluff_bet_size: Math.max(0.2, Math.min(0.8, base_personality.bluff_bet_size + random_factor)),
    fold_threshold: Math.max(0.1, Math.min(0.4, base_personality.fold_threshold + random_factor * 0.5))
  };
}

function decide_strong_hand(player, call_amount, pot_size, personality) {
  if (call_amount === 0) {
    var bet_size = Math.floor(pot_size * personality.value_bet_size);
    bet_size = Math.max(bet_size, BIG_BLIND);
    return { type: 'bet', amount: bet_size, reason: 'Value bet' };
  } else {
    var raise_size = Math.floor(call_amount * (1.5 + Math.random() * 1.5));
    raise_size = Math.max(raise_size, BIG_BLIND);
    return { type: 'raise', call: call_amount, raise: raise_size, reason: 'Value raise' };
  }
}

function decide_medium_hand(player, call_amount, pot_size, hand_strength, personality) {
  var pot_odds = call_amount > 0 ? call_amount / (pot_size + call_amount) : 0;

  if (call_amount === 0) {
    if (hand_strength >= 50 && Math.random() < personality.aggression) {
      var bet_size = Math.floor(pot_size * 0.4);
      bet_size = Math.max(bet_size, BIG_BLIND);
      return { type: 'bet', amount: bet_size, reason: 'Medium hand bet' };
    } else {
      return { type: 'check', reason: 'Check to see' };
    }
  } else {
    var decision_factor = Math.random();

    if (pot_odds < 0.3 && hand_strength >= 45) {
      if (hand_strength >= 60 && decision_factor < personality.aggression * 0.8) {
        var raise_size = Math.floor(call_amount * (1 + Math.random()));
        return { type: 'raise', call: call_amount, raise: raise_size, reason: 'Medium strong raise' };
      } else {
        return { type: 'call', amount: call_amount, reason: 'Medium hand call' };
      }
    } else if (pot_odds < 0.4 && hand_strength >= 35) {
      if (decision_factor < 0.7) {
        return { type: 'call', amount: call_amount, reason: 'Medium hand pot odds call' };
      } else {
        return { type: 'fold', reason: 'Medium hand fold' };
      }
    } else {
      if (decision_factor < 0.2) {
        return { type: 'call', amount: call_amount, reason: 'Medium hand impulse call' };
      } else {
        return { type: 'fold', reason: 'Medium hand fold' };
      }
    }
  }
}

function decide_weak_hand(player, call_amount, pot_size, hand_strength, personality) {
  var pot_odds = call_amount > 0 ? call_amount / (pot_size + call_amount) : 0;

  if (call_amount === 0) {
    if (Math.random() < personality.bluff_frequency) {
      var bluff_size = Math.floor(pot_size * personality.bluff_bet_size);
      bluff_size = Math.max(bluff_size, BIG_BLIND);
      return { type: 'bet', amount: bluff_size, reason: 'Weak hand bluff' };
    } else {
      return { type: 'check', reason: 'Weak hand check' };
    }
  } else {
    var curiosity_factor = Math.random();

    var base_call_chance = 0;
    if (pot_odds < 0.2 && hand_strength >= 25) {
      base_call_chance = 0.6; 
    } else if (pot_odds < 0.3) {
      base_call_chance = 0.3; 
    } else {
      base_call_chance = 0.15; 
    }

    if (curiosity_factor < base_call_chance) {
      return { type: 'call', amount: call_amount, reason: 'Weak hand curious call' };
    } else {
      return { type: 'fold', reason: 'Weak hand fold' };
    }
  }
}

function decide_trash_hand(player, call_amount, pot_size, personality) {
  if (call_amount === 0) {
    if (Math.random() < personality.bluff_frequency * 0.5) {
      var bluff_size = Math.floor(pot_size * personality.bluff_bet_size);
      bluff_size = Math.max(bluff_size, BIG_BLIND);
      return { type: 'bet', amount: bluff_size, reason: 'Trash hand bluff' };
    } else {
      return { type: 'check', reason: 'Trash hand check' };
    }
  } else {
    var pot_odds = call_amount / (pot_size + call_amount);
    var random_factor = Math.random();

    var call_chance = 0;

    if (pot_odds < 0.1 && call_amount <= BIG_BLIND) {
      call_chance = 0.4; 
    } else if (call_amount <= BIG_BLIND) {
      call_chance = 0.25; 
    } else if (pot_odds < 0.2) {
      call_chance = 0.15; 
    } else {
      call_chance = 0.08; 
    }

    if (Math.random() < 0.1) {
      call_chance += 0.1; 
    }

    if (random_factor < call_chance) {
      var reasons = ['Trash hand cheap call', 'Trash hand curious call', 'Trash hand impulse call', 'Trash hand pot odds call'];
      var reason = reasons[Math.floor(Math.random() * reasons.length)];
      return { type: 'call', amount: call_amount, reason: reason };
    } else {
      return { type: 'fold', reason: 'Trash hand fold' };
    }
  }
}

function execute_ai_decision(player_index, action) {
  var current_player = players[player_index];
  var decision_text = '';

  switch (action.type) {
    case 'fold':
      current_player.status = "FOLD";
      decision_text = current_player.name + ' folds';
      show_player_action(player_index, 'fold');
      break;

    case 'check':
      current_player.status = "CALL";
      decision_text = current_player.name + ' checks';
      show_player_action(player_index, 'check');
      break;

    case 'call':
      if (action.amount <= current_player.bankroll) {
        the_bet_function(player_index, action.amount);
        current_player.status = "CALL";
        decision_text = current_player.name + ' calls $' + action.amount;
        show_player_action(player_index, 'call', action.amount);
      } else {
        current_player.status = "FOLD";
        decision_text = current_player.name + ' folds (insufficient funds)';
        show_player_action(player_index, 'fold');
      }
      break;

    case 'bet':
      if (action.amount <= current_player.bankroll) {
        the_bet_function(player_index, action.amount);
        current_player.status = "CALL";
        decision_text = current_player.name + ' bets $' + action.amount;
        show_player_action(player_index, 'bet', action.amount);
        if (action.reason.includes('bluff')) {
          decision_text += ' (bluff)';
        }
      } else {
        current_player.status = "CALL";
        decision_text = current_player.name + ' checks (insufficient funds)';
        show_player_action(player_index, 'check');
      }
      break;

    case 'raise':
      var total_bet = action.call + action.raise;
      if (total_bet <= current_player.bankroll) {
        the_bet_function(player_index, total_bet);
        current_player.status = "CALL";
        decision_text = current_player.name + ' raises $' + action.raise;
        show_player_action(player_index, 'raise', action.raise);
        if (action.reason.includes('bluff')) {
          decision_text += ' (bluff)';
        }
      } else if (action.call <= current_player.bankroll) {
        the_bet_function(player_index, action.call);
        current_player.status = "CALL";
        decision_text = current_player.name + ' calls $' + action.call + ' (insufficient funds)';
        show_player_action(player_index, 'call', action.call);
      } else {
        current_player.status = "FOLD";
        decision_text = current_player.name + ' folds (insufficient funds)';
        show_player_action(player_index, 'fold');
      }
      break;
  }

  update_game_status(decision_text);

  update_ui();

  if (action.type === 'fold') {
    var remaining_players = get_num_betting();
    if (remaining_players <= 1) {
      handle_end_of_round();
      return;
    }
  }

  current_bettor_index = get_next_player_position(current_bettor_index, 1);
  setTimeout(main, 1000);
}

function show_game_interface() {
  var overlay = document.getElementById('game-start-overlay');
  if (overlay) {
    overlay.style.opacity = '0';
    setTimeout(function() {
      overlay.style.display = 'none';
    }, 500);
  }

  for (var i = 0; i < 3; i++) {
    var playerSeat = document.getElementById('player-' + i);
    if (playerSeat) {
      playerSeat.classList.remove('empty-seat');
      playerSeat.classList.add('filled');

      var placeholder = playerSeat.querySelector('.empty-seat-placeholder');
      var playerInfo = playerSeat.querySelector('.player-info');
      if (placeholder) placeholder.style.display = 'none';
      if (playerInfo) playerInfo.style.display = 'flex';
    }
  }

  var controlPanel = document.getElementById('control-panel');
  if (controlPanel) {
    controlPanel.style.display = 'block';
    setTimeout(function() {
      controlPanel.style.opacity = '1';
    }, 100);
  }

  var gameStatus = document.getElementById('game-status');
  if (gameStatus) {
    gameStatus.style.display = 'block';
  }
}

var current_player_count = 6;
var current_table_type = '6-players';

document.addEventListener('DOMContentLoaded', function() {
  show_default_table();

  document.getElementById('start-game-btn').addEventListener('click', function() {
    show_player_count_modal();
  });

  document.getElementById('fold-btn').addEventListener('click', player_fold);
  document.getElementById('call-btn').addEventListener('click', player_call);
  document.getElementById('raise-btn').addEventListener('click', player_raise);

  var count_options = document.querySelectorAll('.count-option');
  for (var i = 0; i < count_options.length; i++) {
    count_options[i].addEventListener('click', function() {
      var selected_count = parseInt(this.getAttribute('data-count'));
      start_game_with_player_count(selected_count);
    });
  }

  var settings_btn = document.getElementById('settings-btn');
  var settings_dropdown = document.getElementById('settings-dropdown');
  var restart_btn = document.getElementById('restart-game-btn');
  var change_players_btn = document.getElementById('change-players-btn');

  if (settings_btn && settings_dropdown) {
    settings_btn.addEventListener('click', function(e) {
      e.stopPropagation();
      settings_dropdown.classList.toggle('show');
    });

    document.addEventListener('click', function() {
      settings_dropdown.classList.remove('show');
    });

    settings_dropdown.addEventListener('click', function(e) {
      e.stopPropagation();
    });
  }

  if (restart_btn) {
    restart_btn.addEventListener('click', function() {
      settings_dropdown.classList.remove('show');
      restart_current_game();
    });
  }

  if (change_players_btn) {
    change_players_btn.addEventListener('click', function() {
      settings_dropdown.classList.remove('show');
      show_player_count_modal();
    });
  }

  document.getElementById('bet-slider').addEventListener('input', function(e) {
    var value = e.target.value;
    var bet_amount = document.getElementById('bet-amount');
    if (bet_amount) bet_amount.textContent = value;
  });
});

function show_player_count_modal() {
  var modal = document.getElementById('player-count-modal');
  if (modal) {
    modal.classList.add('show');
  }
}

function hide_player_count_modal() {
  var modal = document.getElementById('player-count-modal');
  if (modal) {
    modal.classList.remove('show');
  }
}

function start_game_with_player_count(player_count) {
  reset_game_state();

  current_player_count = player_count;
  current_table_type = player_count + '-players';

  hide_player_count_modal();

  var start_btn = document.getElementById('start-game-btn');
  if (start_btn) {
    start_btn.style.display = 'none';
  }

  show_table_for_player_count(player_count);

  show_game_elements();

  new_game_with_player_count(player_count);
}

function show_table_for_player_count(player_count) {
  var all_tables = document.querySelectorAll('.poker-table');
  for (var i = 0; i < all_tables.length; i++) {
    all_tables[i].style.display = 'none';
  }

  var target_table = document.getElementById('table-' + player_count + '-players');
  if (target_table) {
    target_table.style.display = 'flex';
  }
}

function new_game_with_player_count(player_count) {
  players = new Array(player_count);

  for (var i = 0; i < player_count; i++) {
    if (i === 0) {
      players[i] = new player("You", STARTING_BANKROLL, "", "", "", 0, 0);
    } else {
      players[i] = new player("Player " + i, STARTING_BANKROLL, "", "", "", 0, 0);
    }
  }

  setup_control_panel();

  button_index = 0;
  make_deck();
  new_round();
}

function restart_current_game() {
  if (current_player_count) {
    clear_all_player_cards();
    clear_all_player_actions();

    new_game_with_player_count(current_player_count);

    update_game_status('Game Restarted');
  }
}

function reset_game_state() {
  var control_panel = document.getElementById('control-panel');
  if (control_panel) {
    control_panel.style.display = 'none';
  }

  if (players) {
    clear_all_player_cards();
    clear_all_player_actions();
  }

  players = null;
  board = new Array(5);
  current_bet_amount = 0;
  current_min_raise = BIG_BLIND;

  var suffix = get_table_suffix();
  var community_card_ids = [
    '#flop-1' + suffix,
    '#flop-2' + suffix,
    '#flop-3' + suffix,
    '#turn' + suffix,
    '#river' + suffix
  ];

  for (var i = 0; i < community_card_ids.length; i++) {
    var card_element = document.querySelector(community_card_ids[i]);
    if (card_element) {
      card_element.className = 'card-slot';
      card_element.innerHTML = '';
    }
  }

  var pot_display = document.querySelector('.pot-display .pot-amount');
  if (pot_display) {
    pot_display.textContent = '0';
  }
}

function reset_to_player_selection() {
  reset_game_state();

  show_default_table();

  var start_btn = document.getElementById('start-game-btn');
  if (start_btn) {
    start_btn.style.display = 'block';
  }

  update_game_status('Click Start Game');
}

function update_player_chips_display() {
  var chips_display = document.getElementById('player-chips-display');
  if (chips_display && players && players[0]) {
    chips_display.textContent = players[0].bankroll;
  }
}

function show_default_table() {
  var default_table = document.getElementById('table-6-players');
  if (default_table) {
    default_table.style.display = 'flex';
  }

  var other_tables = ['table-3-players', 'table-9-players'];
  for (var i = 0; i < other_tables.length; i++) {
    var table = document.getElementById(other_tables[i]);
    if (table) {
      table.style.display = 'none';
    }
  }

  hide_game_elements();

  current_player_count = 6;
  current_table_type = '6-players';
}

function hide_game_elements() {
  var all_players_areas = document.querySelectorAll('.players-area');
  for (var i = 0; i < all_players_areas.length; i++) {
    all_players_areas[i].style.display = 'none';
  }

  var all_community_cards = document.querySelectorAll('.community-cards');
  for (var i = 0; i < all_community_cards.length; i++) {
    all_community_cards[i].style.display = 'none';
  }

  var all_pot_areas = document.querySelectorAll('.pot-area');
  for (var i = 0; i < all_pot_areas.length; i++) {
    all_pot_areas[i].style.display = 'none';
  }
}

function show_game_elements() {
  var suffix = get_table_suffix();
  var current_table = document.getElementById('table-' + current_player_count + '-players');

  if (current_table) {
    var players_area = current_table.querySelector('.players-area');
    if (players_area) {
      players_area.style.display = 'block';
    }

    var community_cards = current_table.querySelector('.community-cards');
    if (community_cards) {
      community_cards.style.display = 'flex';
    }

    var pot_area = current_table.querySelector('.pot-area');
    if (pot_area) {
      pot_area.style.display = 'block';
    }
  }
}

function setup_control_panel() {
  var control_panel = document.getElementById('control-panel');
  var suffix = get_table_suffix();
  var human_player = document.querySelector('#player-0' + suffix);

  if (control_panel && human_player) {
    human_player.appendChild(control_panel);
  }
}



function evaluate_preflop_hand_strength(hole_cards, num_opponents) {
  if (!hole_cards || hole_cards.length !== 2) return 0.1;

  var card1 = hole_cards[0];
  var card2 = hole_cards[1];
  var rank1 = get_card_rank_value(card1);
  var rank2 = get_card_rank_value(card2);
  var suit1 = get_card_suit(card1);
  var suit2 = get_card_suit(card2);

  var is_pair = rank1 === rank2;
  var is_suited = suit1 === suit2;
  var high_rank = Math.max(rank1, rank2);
  var low_rank = Math.min(rank1, rank2);
  var gap = high_rank - low_rank;

  var base_strength = 0;

  if (is_pair) {
    if (rank1 >= 14) base_strength = 0.85; 
    else if (rank1 >= 13) base_strength = 0.8; 
    else if (rank1 >= 12) base_strength = 0.75; // QQ
    else if (rank1 >= 11) base_strength = 0.7; // JJ
    else if (rank1 >= 10) base_strength = 0.65; // TT
    else if (rank1 >= 8) base_strength = 0.55; // 88-99
    else if (rank1 >= 6) base_strength = 0.45; // 66-77
    else base_strength = 0.35; // 22-55
  }
  else {
    if (high_rank === 14) { // A
      if (low_rank >= 13) base_strength = is_suited ? 0.7 : 0.65; // AK
      else if (low_rank >= 12) base_strength = is_suited ? 0.65 : 0.6; // AQ
      else if (low_rank >= 11) base_strength = is_suited ? 0.6 : 0.55; // AJ
      else if (low_rank >= 10) base_strength = is_suited ? 0.55 : 0.5; // AT
      else if (low_rank >= 8) base_strength = is_suited ? 0.45 : 0.35; // A8-A9
      else base_strength = is_suited ? 0.35 : 0.25; // A2-A7
    }
    else if (high_rank >= 13) { // K
      if (low_rank >= 12) base_strength = is_suited ? 0.6 : 0.55; // KQ
      else if (low_rank >= 11) base_strength = is_suited ? 0.55 : 0.5; // KJ
      else if (low_rank >= 10) base_strength = is_suited ? 0.5 : 0.45; // KT
      else if (low_rank >= 8) base_strength = is_suited ? 0.45 : 0.35; // K8-K9
      else base_strength = is_suited ? 0.35 : 0.25; // K2-K7
    }
    else if (high_rank >= 12) { // Q
      if (low_rank >= 11) base_strength = is_suited ? 0.5 : 0.45; // QJ
      else if (low_rank >= 10) base_strength = is_suited ? 0.45 : 0.4; // QT
      else if (low_rank >= 8) base_strength = is_suited ? 0.4 : 0.3; // Q8-Q9
      else base_strength = is_suited ? 0.3 : 0.2; // Q2-Q7
    }
    else if (high_rank >= 11) { // J
      if (low_rank >= 10) base_strength = is_suited ? 0.4 : 0.35; // JT
      else if (low_rank >= 8) base_strength = is_suited ? 0.35 : 0.25; // J8-J9
      else base_strength = is_suited ? 0.25 : 0.15; // J2-J7
    }
    else if (high_rank >= 10) { // T
      if (low_rank >= 9) base_strength = is_suited ? 0.35 : 0.3; // T9
      else if (low_rank >= 8) base_strength = is_suited ? 0.3 : 0.25; // T8
      else base_strength = is_suited ? 0.25 : 0.15; // T2-T7
    }
    else if (high_rank >= 9) { // 9
      if (low_rank >= 8) base_strength = is_suited ? 0.3 : 0.25; // 98
      else if (low_rank >= 7) base_strength = is_suited ? 0.25 : 0.2; // 97
      else base_strength = is_suited ? 0.2 : 0.12; // 92-96
    }
    else if (high_rank >= 8) { // 8
      if (low_rank >= 7) base_strength = is_suited ? 0.25 : 0.2; // 87
      else base_strength = is_suited ? 0.2 : 0.12; // 82-86
    }
    else if (high_rank >= 7) { // 7
      if (low_rank >= 6) base_strength = is_suited ? 0.2 : 0.15; // 76
      else base_strength = is_suited ? 0.15 : 0.1; // 72-75
    }
    else {
      base_strength = is_suited ? 0.15 : 0.08;
    }
  }


  if (!is_pair && base_strength < 0.3) {
    if (is_suited && gap <= 4) {
      base_strength = Math.max(base_strength, 0.25); 
    } else if (gap <= 1 && high_rank >= 8) {
      base_strength = Math.max(base_strength, 0.2); 
    }
  }

  var opponent_factor = Math.max(0.7, 1 - (num_opponents - 1) * 0.05);
  base_strength *= opponent_factor;

  return Math.min(0.9, Math.max(0.1, base_strength));
}

function get_card_rank_value(card) {
  if (!card) return 0;
  var rank = card.charAt(0);
  switch(rank) {
    case 'A': return 14;
    case 'K': return 13;
    case 'Q': return 12;
    case 'J': return 11;
    case 'T': return 10;
    default: return parseInt(rank);
  }
}

function get_card_suit(card) {
  if (!card) return '';
  return card.charAt(1);
}

function monte_carlo_win_rate(hole_cards, community_cards, num_opponents, simulations) {
  if (!hole_cards || hole_cards.length !== 2) return 0;

  var wins = 0;
  var ties = 0;

  var known_cards = hole_cards.concat(community_cards.filter(function(card) { return card; }));

  for (var sim = 0; sim < simulations; sim++) {
    var remaining_deck = create_remaining_deck(known_cards);

    var sim_board = community_cards.slice();
    var deck_index = 0;

    while (sim_board.filter(function(card) { return card; }).length < 5) {
      if (deck_index < remaining_deck.length) {
        sim_board.push(remaining_deck[deck_index++]);
      }
    }

    var opponents = [];
    for (var i = 0; i < num_opponents; i++) {
      if (deck_index + 1 < remaining_deck.length) {
        opponents.push([remaining_deck[deck_index++], remaining_deck[deck_index++]]);
      }
    }

    var my_hand = evaluate_hand(hole_cards, sim_board);
    var opponent_hands = [];

    for (var i = 0; i < opponents.length; i++) {
      opponent_hands.push(evaluate_hand(opponents[i], sim_board));
    }

    var is_winner = true;
    var is_tie = false;

    for (var i = 0; i < opponent_hands.length; i++) {
      var comparison = compare_hands(my_hand, opponent_hands[i]);
      if (comparison < 0) {
        is_winner = false;
        break;
      } else if (comparison === 0) {
        is_tie = true;
      }
    }

    if (is_winner) {
      if (is_tie) {
        ties++;
      } else {
        wins++;
      }
    }
  }

  return {
    win_rate: (wins + ties * 0.5) / simulations,
    wins: wins,
    ties: ties,
    simulations: simulations
  };
}

function create_remaining_deck(known_cards) {
  var full_deck = [];
  var suits = ['h', 'd', 'c', 's'];

  for (var i = 0; i < suits.length; i++) {
    for (var rank = 2; rank <= 14; rank++) {
      full_deck.push(suits[i] + rank);
    }
  }

  var remaining = [];
  for (var i = 0; i < full_deck.length; i++) {
    if (known_cards.indexOf(full_deck[i]) === -1) {
      remaining.push(full_deck[i]);
    }
  }

  for (var i = remaining.length - 1; i > 0; i--) {
    var j = Math.floor(Math.random() * (i + 1));
    var temp = remaining[i];
    remaining[i] = remaining[j];
    remaining[j] = temp;
  }

  return remaining;
}

function compare_hands(hand1, hand2) {
  if (hand1.rank > hand2.rank) return 1;
  if (hand1.rank < hand2.rank) return -1;

  return compare_tie_breakers(hand1.tie_breakers, hand2.tie_breakers);
}

function get_strategy_decision(win_rate, pot_odds, position, betting_history, personality) {
  var decision = {
    action: 'fold',
    aggression: 0,
    bluff_probability: 0,
    reasoning: ''
  };

  if (win_rate >= 0.6) {
    decision.action = 'aggressive_bet';
    decision.aggression = 0.9;
    decision.reasoning = 'Very strong hand value bet';
  } else if (win_rate >= 0.4) {
    decision.action = 'value_bet';
    decision.aggression = 0.7;
    decision.reasoning = 'Strong hand value bet';
  } else if (win_rate >= 0.25) {
    if (pot_odds < 0.6) {
      decision.action = 'call';
      decision.aggression = 0.4;
      decision.reasoning = 'Medium hand call';
    } else {
      decision.action = 'fold';
      decision.reasoning = 'Medium hand poor odds';
    }
  } else if (win_rate >= 0.12) {
    if (pot_odds < 0.5) {
      decision.action = 'call';
      decision.aggression = 0.2;
      decision.reasoning = 'Weak hand cheap call';
    } else {
      decision.action = 'fold';
      decision.reasoning = 'Weak hand fold';
    }
  } else {
    if (pot_odds < 0.3 && Math.random() < (personality.bluff_frequency * 2)) {
      decision.action = 'call';
      decision.aggression = 0.1;
      decision.reasoning = 'Trash hand bluff call';
    } else {
      decision.action = 'fold';
      decision.bluff_probability = personality.bluff_frequency * 2;
      decision.reasoning = 'Trash hand fold';
    }
  }

  if (position === 'late') {
    decision.aggression *= 1.2;
    decision.bluff_probability *= 1.5;
  } else if (position === 'early') {
    decision.aggression *= 0.8;
    decision.bluff_probability *= 0.7;
  }

  if (betting_history.recent_aggression > 0.7) {
    decision.aggression *= 0.7;
    decision.bluff_probability *= 0.5;
  } else if (betting_history.recent_aggression < 0.3) {
    decision.aggression *= 1.3;
    decision.bluff_probability *= 1.2;
  }

  return decision;
}

function analyze_betting_history(player_index) {
  var recent_actions = [];
  var aggression_score = 0;

  return {
    recent_aggression: 0.5,
    fold_frequency: 0.3,
    bluff_frequency: 0.1,
    recent_actions: recent_actions
  };
}

function get_position_info(player_index) {
  var players_after = 0;
  var temp_index = player_index;

  for (var i = 0; i < players.length - 1; i++) {
    temp_index = get_next_player_position(temp_index, 1);
    if (players[temp_index].status !== "FOLD" && players[temp_index].status !== "BUST") {
      players_after++;
    }
  }

  if (players_after <= 1) return 'late';
  if (players_after >= 2) return 'early';
  return 'middle';
}

function advanced_ai_decision(player_index) {
  var current_player = players[player_index];
  var hole_cards = [current_player.carda, current_player.cardb];
  var call_amount = Math.max(0, current_bet_amount - current_player.subtotal_bet);

  var total_pot = 0;
  for (var i = 0; i < players.length; i++) {
    total_pot += players[i].total_bet + players[i].subtotal_bet;
  }
  var pot_odds = call_amount > 0 ? call_amount / (total_pot + call_amount) : 0;

  var num_opponents = get_num_betting() - 1;

  var win_rate;
  if (board.filter(function(card) { return card; }).length === 0) {
    win_rate = evaluate_preflop_hand_strength(hole_cards, num_opponents);
  } else {
    var win_rate_result = monte_carlo_win_rate(hole_cards, board, num_opponents, 1000);
    win_rate = win_rate_result.win_rate;
  }

  var personality = get_ai_personality(player_index);
  var position = get_position_info(player_index);
  var betting_history = analyze_betting_history(player_index);

  var strategy = get_strategy_decision(win_rate, pot_odds, position, betting_history, personality);

  var final_action = apply_behavioral_model(strategy, win_rate, pot_odds, personality, current_player);

  return final_action;
}

function apply_behavioral_model(strategy, win_rate, pot_odds, personality, player) {
  var action = {
    type: 'fold',
    amount: 0,
    reason: 'Default fold'
  };

  switch (strategy.action) {
    case 'aggressive_bet':
      action = create_aggressive_bet_action(win_rate, pot_odds, personality, player);
      break;
    case 'value_bet':
      action = create_value_bet_action(win_rate, pot_odds, personality, player);
      break;
    case 'call':
      action = create_call_action(win_rate, pot_odds, personality, player);
      break;
    case 'fold':
      action = create_fold_action(strategy, personality, player);
      break;
  }

  action = add_human_factors(action, strategy, win_rate, personality);

  return action;
}

function create_aggressive_bet_action(win_rate, pot_odds, personality, player) {
  var call_amount = Math.max(0, current_bet_amount - player.subtotal_bet);
  var total_pot = 0;
  for (var i = 0; i < players.length; i++) {
    total_pot += players[i].total_bet + players[i].subtotal_bet;
  }

  if (call_amount === 0) {
    var bet_size = Math.floor(total_pot * (0.6 + personality.aggression * 0.4));
    bet_size = Math.max(bet_size, BIG_BLIND);
    return { type: 'bet', amount: bet_size, reason: 'Strong hand value bet' };
  } else {
    var raise_size = Math.floor(call_amount * (2 + personality.aggression));
    return { type: 'raise', call: call_amount, raise: raise_size, reason: 'Strong hand value raise' };
  }
}

function create_value_bet_action(win_rate, pot_odds, personality, player) {
  var call_amount = Math.max(0, current_bet_amount - player.subtotal_bet);
  var total_pot = 0;
  for (var i = 0; i < players.length; i++) {
    total_pot += players[i].total_bet + players[i].subtotal_bet;
  }

  if (call_amount === 0) {
    var bet_size = Math.floor(total_pot * (0.4 + personality.aggression * 0.3));
    bet_size = Math.max(bet_size, BIG_BLIND);
    return { type: 'bet', amount: bet_size, reason: 'Medium strong value bet' };
  } else {
    if (pot_odds < 0.4) {
      return { type: 'call', amount: call_amount, reason: 'Medium strong call' };
    } else {
      return { type: 'fold', reason: 'Medium strong poor odds fold' };
    }
  }
}

function create_call_action(win_rate, pot_odds, personality, player) {
  var call_amount = Math.max(0, current_bet_amount - player.subtotal_bet);

  if (call_amount === 0) {
    return { type: 'check', reason: 'Medium hand check' };
  } else {
    if (call_amount <= player.bankroll) {
      return { type: 'call', amount: call_amount, reason: 'Medium hand call' };
    } else {
      return { type: 'fold', reason: 'Insufficient funds fold' };
    }
  }
}

function create_fold_action(strategy, personality, player) {
  var call_amount = Math.max(0, current_bet_amount - player.subtotal_bet);

  if (Math.random() < strategy.bluff_probability) {
    var total_pot = 0;
    for (var i = 0; i < players.length; i++) {
      total_pot += players[i].total_bet + players[i].subtotal_bet;
    }

    if (call_amount === 0) {
      var bluff_size = Math.floor(total_pot * personality.bluff_bet_size);
      bluff_size = Math.max(bluff_size, BIG_BLIND);
      return { type: 'bet', amount: bluff_size, reason: 'Weak hand bluff' };
    } else if (call_amount <= player.bankroll * 0.1) {
      return { type: 'call', amount: call_amount, reason: 'Weak hand bluff call' };
    }
  }

  return { type: 'fold', reason: 'Weak hand fold' };
}

function add_human_factors(action, strategy, win_rate, personality) {
  var randomness = (Math.random() - 0.5) * 0.1; 

  if (action.type === 'bet' || action.type === 'raise') {
    if (action.amount) {
      action.amount = Math.floor(action.amount * (1 + randomness));
      action.amount = Math.max(action.amount, BIG_BLIND);
    }
    if (action.raise) {
      action.raise = Math.floor(action.raise * (1 + randomness));
      action.raise = Math.max(action.raise, BIG_BLIND);
    }
  }

  return action;
}

function get_player_positions(count) {
  switch (count) {
    case 3:
      return [0, 2, 3]; 
    case 6:
      return [0, 1, 2, 3, 4, 5]; 
    case 9:
      return [0, 1, 2, 3, 4, 5, 6, 7, 8]; 
    default:
      return [0, 1, 2, 3, 4, 5]; 
  }
}

function show_player_positions(count) {
  var playersArea = document.querySelector('.players-area');
  if (!playersArea) return;

  playersArea.classList.remove('count-3', 'count-6', 'count-9');

  playersArea.classList.add('count-' + count);

  for (var i = 0; i < 9; i++) {
    var player = document.getElementById('player-' + i);
    if (player) {
      player.style.display = 'none';
    }
  }

  var positions = get_player_positions(count);
  for (var i = 0; i < positions.length; i++) {
    var playerId = positions[i];
    var player = document.getElementById('player-' + playerId);
    if (player) {
      player.style.display = 'block';
    }
  }
}

// 横屏处理逻辑
function handleOrientationChange() {
  var isMobile = window.innerWidth <= 768;
  var isPortrait = window.innerHeight > window.innerWidth;
  var rotatePrompt = document.getElementById('rotate-prompt');
  var gameContainer = document.querySelector('.game-container');

  if (isMobile && isPortrait) {
    // 移动端竖屏：显示横屏提示
    if (rotatePrompt) rotatePrompt.style.display = 'flex';
    if (gameContainer) gameContainer.style.display = 'none';
  } else if (isMobile && !isPortrait) {
    // 移动端横屏：显示游戏内容
    if (rotatePrompt) rotatePrompt.style.display = 'none';
    if (gameContainer) gameContainer.style.display = 'flex';
  } else {
    // 桌面端：始终显示游戏内容
    if (rotatePrompt) rotatePrompt.style.display = 'none';
    if (gameContainer) gameContainer.style.display = 'flex';
  }
}

// 初始化横屏处理
function initOrientationHandling() {
  handleOrientationChange();
  window.addEventListener('resize', function() {
    setTimeout(handleOrientationChange, 100);
  });
  window.addEventListener('orientationchange', function() {
    setTimeout(handleOrientationChange, 100);
  });
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initOrientationHandling);
} else {
  initOrientationHandling();
}

